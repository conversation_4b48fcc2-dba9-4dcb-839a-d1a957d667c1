import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Waiting players queue for matchmaking
  waitingPlayers: defineTable({
    walletAddress: v.string(),
    wagerAmount: v.number(),
    joinedAt: v.number(),
    status: v.union(
      v.literal("waiting"),
      v.literal("matched"),
      v.literal("cancelled"),
    ),
  })
    .index("by_wager_and_status", ["wagerAmount", "status"])
    .index("by_wallet", ["walletAddress"])
    .index("by_status", ["status"]),

  // Active game sessions
  gameSessions: defineTable({
    player1Wallet: v.string(),
    player2Wallet: v.string(),
    wagerAmount: v.number(),
    gameState: v.union(
      v.literal("starting"),
      v.literal("playing"),
      v.literal("finished"),
      v.literal("cancelled"),
    ),
    currentPlayer: v.union(v.literal(1), v.literal(2)),
    board: v.array(v.array(v.number())), // 6x7 board
    winner: v.optional(v.union(v.literal(1), v.literal(2), v.literal("draw"))),
    isDraw: v.optional(v.boolean()),
    totalMoves: v.number(),
    createdAt: v.number(),
    lastMoveAt: v.number(),
    turnStartTime: v.optional(v.number()), // When current turn started
    turnTimeLimit: v.optional(v.number()), // Time limit per turn (10 seconds)
  })
    .index("by_players", ["player1Wallet", "player2Wallet"])
    .index("by_player1", ["player1Wallet"])
    .index("by_player2", ["player2Wallet"])
    .index("by_state", ["gameState"]),

  // Game moves history
  gameMoves: defineTable({
    gameId: v.id("gameSessions"),
    player: v.union(v.literal(1), v.literal(2)),
    column: v.number(),
    row: v.number(),
    moveNumber: v.number(),
    timestamp: v.number(),
  })
    .index("by_game", ["gameId"])
    .index("by_game_and_move", ["gameId", "moveNumber"]),
});
