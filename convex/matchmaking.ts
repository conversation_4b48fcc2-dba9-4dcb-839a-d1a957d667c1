import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Join the matchmaking queue
export const joinQueue = mutation({
  args: {
    walletAddress: v.string(),
    wagerAmount: v.number(),
  },
  handler: async (ctx, { walletAddress, wagerAmount }) => {
    // Check if player is already in queue for this wager
    const existingPlayer = await ctx.db
      .query("waitingPlayers")
      .withIndex("by_wallet", (q) => q.eq("walletAddress", walletAddress))
      .filter((q) =>
        q.and(
          q.eq(q.field("wagerAmount"), wagerAmount),
          q.eq(q.field("status"), "waiting"),
        ),
      )
      .first();

    if (existingPlayer) {
      console.log(
        `Player ${walletAddress.slice(0, 8)}... already in queue for ${wagerAmount} SOL`,
      );
      return { playerId: existingPlayer._id, matched: false };
    }

    // Check if player is already in an active game
    const activeGame = await ctx.db
      .query("gameSessions")
      .filter((q) =>
        q.and(
          q.or(
            q.eq(q.field("player1Wallet"), walletAddress),
            q.eq(q.field("player2Wallet"), walletAddress),
          ),
          q.neq(q.field("gameState"), "finished"),
        ),
      )
      .first();

    if (activeGame) {
      console.log(
        `Player ${walletAddress.slice(0, 8)}... already in active game`,
      );
      return {
        playerId: "existing",
        matched: true,
        gameId: activeGame._id,
        opponent:
          activeGame.player1Wallet === walletAddress
            ? activeGame.player2Wallet
            : activeGame.player1Wallet,
      };
    }

    // Add player to queue
    const playerId = await ctx.db.insert("waitingPlayers", {
      walletAddress,
      wagerAmount,
      joinedAt: Date.now(),
      status: "waiting",
    });

    console.log(
      `Player ${walletAddress.slice(0, 8)}... joined queue for ${wagerAmount} SOL`,
    );

    // Check for immediate match with OTHER players (not the same wallet)
    const waitingPlayers = await ctx.db
      .query("waitingPlayers")
      .withIndex("by_wager_and_status", (q) =>
        q.eq("wagerAmount", wagerAmount).eq("status", "waiting"),
      )
      .filter((q) => q.neq(q.field("walletAddress"), walletAddress))
      .collect();

    console.log(
      `Found ${waitingPlayers.length} other players waiting for ${wagerAmount} SOL`,
    );

    // If we have 1+ OTHER players, create a match
    if (waitingPlayers.length >= 1) {
      const opponent = waitingPlayers[0];
      const currentPlayer = await ctx.db.get(playerId);

      if (!currentPlayer) {
        throw new Error("Current player not found");
      }

      console.log(
        `Creating match between ${currentPlayer.walletAddress.slice(0, 8)}... and ${opponent.walletAddress.slice(0, 8)}...`,
      );

      // Create game session
      const now = Date.now();
      const gameId = await ctx.db.insert("gameSessions", {
        player1Wallet: currentPlayer.walletAddress,
        player2Wallet: opponent.walletAddress,
        wagerAmount,
        gameState: "starting",
        currentPlayer: 1,
        board: Array(6)
          .fill(null)
          .map(() => Array(7).fill(0)),
        totalMoves: 0,
        createdAt: now,
        lastMoveAt: now,
        turnStartTime: now,
        turnTimeLimit: 10000, // 10 seconds in milliseconds
      });

      // Mark players as matched
      await ctx.db.patch(currentPlayer._id, { status: "matched" });
      await ctx.db.patch(opponent._id, { status: "matched" });

      console.log(`Match created! Game ID: ${gameId}`);

      return {
        playerId,
        matched: true,
        gameId,
        opponent: opponent.walletAddress,
      };
    }

    return { playerId, matched: false };
  },
});

// Leave the matchmaking queue
export const leaveQueue = mutation({
  args: {
    walletAddress: v.string(),
  },
  handler: async (ctx, { walletAddress }) => {
    const waitingPlayer = await ctx.db
      .query("waitingPlayers")
      .withIndex("by_wallet", (q) => q.eq("walletAddress", walletAddress))
      .filter((q) => q.eq(q.field("status"), "waiting"))
      .first();

    if (waitingPlayer) {
      await ctx.db.patch(waitingPlayer._id, { status: "cancelled" });
      console.log(`Player ${walletAddress.slice(0, 8)}... left queue`);
    }
  },
});

// Get waiting player counts for each wager amount
export const getWaitingCounts = query({
  args: {},
  handler: async (ctx) => {
    const waitingPlayers = await ctx.db
      .query("waitingPlayers")
      .withIndex("by_status", (q) => q.eq("status", "waiting"))
      .collect();

    const counts: Record<number, number> = {};

    for (const player of waitingPlayers) {
      counts[player.wagerAmount] = (counts[player.wagerAmount] || 0) + 1;
    }

    return counts;
  },
});

// Get waiting players for a specific wager (for debugging)
export const getWaitingPlayersForWager = query({
  args: {
    wagerAmount: v.number(),
  },
  handler: async (ctx, { wagerAmount }) => {
    return await ctx.db
      .query("waitingPlayers")
      .withIndex("by_wager_and_status", (q) =>
        q.eq("wagerAmount", wagerAmount).eq("status", "waiting"),
      )
      .collect();
  },
});

// Check if player found a match
export const checkForMatch = query({
  args: {
    walletAddress: v.string(),
  },
  handler: async (ctx, { walletAddress }) => {
    if (!walletAddress) {
      return { matched: false };
    }

    // Check if player is in a game session
    const gameAsPlayer1 = await ctx.db
      .query("gameSessions")
      .withIndex("by_player1", (q) => q.eq("player1Wallet", walletAddress))
      .filter((q) => q.neq(q.field("gameState"), "finished"))
      .first();

    if (gameAsPlayer1) {
      console.log(`Found game for ${walletAddress.slice(0, 8)}... as player 1`);
      return {
        matched: true,
        gameId: gameAsPlayer1._id,
        opponent: gameAsPlayer1.player2Wallet,
        playerNumber: 1,
      };
    }

    const gameAsPlayer2 = await ctx.db
      .query("gameSessions")
      .withIndex("by_player2", (q) => q.eq("player2Wallet", walletAddress))
      .filter((q) => q.neq(q.field("gameState"), "finished"))
      .first();

    if (gameAsPlayer2) {
      console.log(`Found game for ${walletAddress.slice(0, 8)}... as player 2`);
      return {
        matched: true,
        gameId: gameAsPlayer2._id,
        opponent: gameAsPlayer2.player1Wallet,
        playerNumber: 2,
      };
    }

    return { matched: false };
  },
});

// Clear all waiting players and games (for testing)
export const clearAll = mutation({
  args: {},
  handler: async (ctx) => {
    // Clear all waiting players
    const waitingPlayers = await ctx.db.query("waitingPlayers").collect();
    for (const player of waitingPlayers) {
      await ctx.db.delete(player._id);
    }

    // Clear all game sessions
    const games = await ctx.db.query("gameSessions").collect();
    for (const game of games) {
      await ctx.db.delete(game._id);
    }

    // Clear all game moves
    const moves = await ctx.db.query("gameMoves").collect();
    for (const move of moves) {
      await ctx.db.delete(move._id);
    }

    console.log("Cleared all data for testing");
    return { success: true };
  },
});
