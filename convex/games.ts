import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Get game session by ID
export const getGame = query({
  args: {
    gameId: v.id("gameSessions"),
  },
  handler: async (ctx, { gameId }) => {
    return await ctx.db.get(gameId);
  },
});

// Make a move in the game
export const makeMove = mutation({
  args: {
    gameId: v.id("gameSessions"),
    walletAddress: v.string(),
    column: v.number(),
  },
  handler: async (ctx, { gameId, walletAddress, column }) => {
    const game = await ctx.db.get(gameId);
    if (!game) {
      throw new Error("Game not found");
    }

    // Verify it's the player's turn
    const isPlayer1 = game.player1Wallet === walletAddress;
    const isPlayer2 = game.player2Wallet === walletAddress;

    if (!isPlayer1 && !isPlayer2) {
      throw new Error("Player not in this game");
    }

    const playerNumber = isPlayer1 ? 1 : 2;
    if (game.currentPlayer !== playerNumber) {
      throw new Error("Not your turn");
    }

    // Prevent rapid-fire moves (minimum 500ms between moves)
    const now = Date.now();
    if (game.lastMoveAt && now - game.lastMoveAt < 500) {
      throw new Error("Please wait before making another move");
    }

    // Check if column is valid and not full
    if (column < 0 || column >= 7) {
      throw new Error("Invalid column");
    }

    if (game.board[0][column] !== 0) {
      throw new Error("Column is full");
    }

    // Find the row where the piece will land
    let targetRow = -1;
    for (let row = 5; row >= 0; row--) {
      if (game.board[row][column] === 0) {
        targetRow = row;
        break;
      }
    }

    if (targetRow === -1) {
      throw new Error("Column is full");
    }

    // Update the board
    const newBoard = game.board.map((row) => [...row]);
    newBoard[targetRow][column] = playerNumber;

    // Check for winner
    const winner = checkWinner(newBoard, targetRow, column, playerNumber);

    // Check for draw - either board is full OR no more moves possible
    const isBoardFull = newBoard[0].every((cell) => cell !== 0);
    const isDraw = !winner && isBoardFull;

    // Update game state
    const now = Date.now();
    const updates: any = {
      board: newBoard,
      currentPlayer: playerNumber === 1 ? 2 : 1,
      totalMoves: game.totalMoves + 1,
      lastMoveAt: now,
      turnStartTime: now, // Reset turn timer for next player
    };

    if (winner) {
      updates.winner = winner;
      updates.gameState = "finished";
    } else if (isDraw) {
      updates.winner = null;
      updates.isDraw = true;
      updates.gameState = "finished";
    }

    await ctx.db.patch(gameId, updates);

    // Record the move
    await ctx.db.insert("gameMoves", {
      gameId,
      player: playerNumber,
      column,
      row: targetRow,
      moveNumber: game.totalMoves + 1,
      timestamp: Date.now(),
    });

    return {
      success: true,
      winner,
      isDraw,
      newBoard,
    };
  },
});

// Start the game (change from starting to playing)
export const startGame = mutation({
  args: {
    gameId: v.id("gameSessions"),
  },
  handler: async (ctx, { gameId }) => {
    const now = Date.now();
    await ctx.db.patch(gameId, {
      gameState: "playing",
      turnStartTime: now,
      turnTimeLimit: 10000, // 10 seconds
    });
  },
});

// Auto-drop when timer expires (server-side)
export const autoDropOnTimeout = mutation({
  args: {
    gameId: v.id("gameSessions"),
  },
  handler: async (ctx, { gameId }) => {
    const game = await ctx.db.get(gameId);
    if (!game) {
      throw new Error("Game not found");
    }

    // Check if game is still active
    if (game.gameState !== "playing") {
      return { success: false, reason: "Game not active" };
    }

    // Check if turn timer has actually expired
    const now = Date.now();
    const turnElapsed = now - (game.turnStartTime || 0);
    const timeLimit = game.turnTimeLimit || 10000;

    if (turnElapsed < timeLimit) {
      return { success: false, reason: "Timer not expired yet" };
    }

    // Find available columns for auto-drop
    const availableColumns = [];
    for (let col = 0; col < 7; col++) {
      if (game.board[0][col] === 0) {
        availableColumns.push(col);
      }
    }

    if (availableColumns.length === 0) {
      // No moves possible - force draw
      await ctx.db.patch(gameId, {
        gameState: "finished",
        isDraw: true,
        winner: null,
      });
      return { success: true, reason: "No moves possible - draw" };
    }

    // Pick random column for auto-drop
    const randomCol =
      availableColumns[Math.floor(Math.random() * availableColumns.length)];

    // Find the row where the piece will land
    let targetRow = -1;
    for (let row = 5; row >= 0; row--) {
      if (game.board[row][randomCol] === 0) {
        targetRow = row;
        break;
      }
    }

    // Update the board
    const newBoard = game.board.map((row) => [...row]);
    newBoard[targetRow][randomCol] = game.currentPlayer;

    // Check for winner
    const winner = checkWinner(
      newBoard,
      targetRow,
      randomCol,
      game.currentPlayer,
    );
    const isBoardFull = newBoard[0].every((cell) => cell !== 0);
    const isDraw = !winner && isBoardFull;

    // Update game state
    const updates: any = {
      board: newBoard,
      currentPlayer: game.currentPlayer === 1 ? 2 : 1,
      totalMoves: game.totalMoves + 1,
      lastMoveAt: now,
      turnStartTime: now, // Reset timer for next player
    };

    if (winner) {
      updates.winner = winner;
      updates.gameState = "finished";
    } else if (isDraw) {
      updates.winner = null;
      updates.isDraw = true;
      updates.gameState = "finished";
    }

    await ctx.db.patch(gameId, updates);

    // Record the auto-drop move
    await ctx.db.insert("gameMoves", {
      gameId,
      player: game.currentPlayer,
      column: randomCol,
      row: targetRow,
      moveNumber: game.totalMoves + 1,
      timestamp: now,
    });

    return {
      success: true,
      reason: "Auto-drop completed",
      column: randomCol,
      winner,
      isDraw,
    };
  },
});

// Helper function to check for winner
function checkWinner(
  board: number[][],
  row: number,
  col: number,
  player: number,
): number | null {
  const directions = [
    [0, 1], // horizontal
    [1, 0], // vertical
    [1, 1], // diagonal \
    [1, -1], // diagonal /
  ];

  for (const [dx, dy] of directions) {
    let count = 1; // Count the piece we just placed

    // Check in positive direction
    for (let i = 1; i < 4; i++) {
      const newRow = row + dx * i;
      const newCol = col + dy * i;
      if (
        newRow >= 0 &&
        newRow < 6 &&
        newCol >= 0 &&
        newCol < 7 &&
        board[newRow][newCol] === player
      ) {
        count++;
      } else {
        break;
      }
    }

    // Check in negative direction
    for (let i = 1; i < 4; i++) {
      const newRow = row - dx * i;
      const newCol = col - dy * i;
      if (
        newRow >= 0 &&
        newRow < 6 &&
        newCol >= 0 &&
        newCol < 7 &&
        board[newRow][newCol] === player
      ) {
        count++;
      } else {
        break;
      }
    }

    if (count >= 4) {
      return player;
    }
  }

  return null;
}
