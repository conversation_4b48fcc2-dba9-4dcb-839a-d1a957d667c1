"use client";

import { useState, useEffect } from "react";
import {
  Connection,
  Keypair,
  PublicKey,
  LAMPORTS_PER_SOL,
  SystemProgram,
  Transaction,
  sendAndConfirmTransaction,
} from "@solana/web3.js";
import { GAME_CONFIG } from "../config/game";
import * as bip39 from "bip39";
import { derivePath } from "ed25519-hd-key";

interface CustomWalletState {
  connected: boolean;
  address: string | null;
  balance: number;
  seedPhrase: string | null;
  privateKey: string | null;
  keypair: Keypair | null;
}

interface CustomWalletHook {
  walletState: CustomWalletState;
  createWallet: () => Promise<void>;
  connectExistingWallet: (privateKey: string) => Promise<void>;
  connectWithSeedPhrase: (seedPhrase: string) => Promise<void>;
  getBalance: () => Promise<number>;
  sendGameTransaction: (
    type: "stake" | "payout",
    amount: number,
    recipientAddress?: string,
  ) => Promise<string>;
  requestAirdrop: () => Promise<boolean>;
  disconnect: () => void;
  exportPrivateKey: () => string | null;
  exportSeedPhrase: () => string | null;
}

const connection = new Connection(
  process.env.NEXT_PUBLIC_SOLANA_RPC_URL || "https://api.devnet.solana.com",
  "confirmed",
);

export function useCustomWallet(): CustomWalletHook {
  const [walletState, setWalletState] = useState<CustomWalletState>({
    connected: false,
    address: null,
    balance: 0,
    seedPhrase: null,
    privateKey: null,
    keypair: null,
  });

  // Load wallet from localStorage on mount
  useEffect(() => {
    const savedSeedPhrase = localStorage.getItem("custom_wallet_seed_phrase");
    const savedPrivateKey = localStorage.getItem("custom_wallet_private_key");
    const savedBalance = localStorage.getItem("custom_wallet_balance");

    if (savedSeedPhrase) {
      connectWithSeedPhrase(savedSeedPhrase).then(() => {
        // Restore saved balance after connection
        if (savedBalance) {
          const balance = parseFloat(savedBalance);
          console.log(`Restored balance from localStorage: ${balance} SOL`);
          setWalletState((prev) => ({ ...prev, balance }));
        }
      });
    } else if (savedPrivateKey) {
      connectExistingWallet(savedPrivateKey).then(() => {
        // Restore saved balance after connection
        if (savedBalance) {
          const balance = parseFloat(savedBalance);
          console.log(`Restored balance from localStorage: ${balance} SOL`);
          setWalletState((prev) => ({ ...prev, balance }));
        }
      });
    } else if (savedBalance) {
      // If no wallet but balance exists, clear it
      localStorage.removeItem("custom_wallet_balance");
    }
  }, []);

  const createWallet = async () => {
    try {
      // Generate new seed phrase
      const seedPhrase = bip39.generateMnemonic();

      // Derive keypair from seed phrase
      const seed = await bip39.mnemonicToSeed(seedPhrase);
      const derivedSeed = derivePath(
        "m/44'/501'/0'/0'",
        seed.toString("hex"),
      ).key;
      const keypair = Keypair.fromSeed(derivedSeed);

      const privateKeyArray = Array.from(keypair.secretKey);
      const privateKeyString = JSON.stringify(privateKeyArray);

      // Save to localStorage
      localStorage.setItem("custom_wallet_seed_phrase", seedPhrase);
      localStorage.setItem("custom_wallet_private_key", privateKeyString);

      // Update state
      setWalletState({
        connected: true,
        address: keypair.publicKey.toString(),
        balance: 0,
        seedPhrase: seedPhrase,
        privateKey: privateKeyString,
        keypair: keypair,
      });

      // Get initial balance
      await getBalance();

      console.log("Created new wallet:", keypair.publicKey.toString());
      console.log("Seed phrase:", seedPhrase);
      console.log("Use the faucet button to add SOL for testing");
    } catch (error) {
      console.error("Failed to create wallet:", error);
      throw error;
    }
  };

  const connectExistingWallet = async (privateKey: string) => {
    try {
      const privateKeyArray = JSON.parse(privateKey);
      const keypair = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));

      setWalletState({
        connected: true,
        address: keypair.publicKey.toString(),
        balance: 0,
        seedPhrase: null, // Unknown for imported private key
        privateKey: privateKey,
        keypair: keypair,
      });

      await getBalance();
      console.log("Connected existing wallet:", keypair.publicKey.toString());
    } catch (error) {
      console.error("Failed to connect existing wallet:", error);
      throw error;
    }
  };

  const connectWithSeedPhrase = async (seedPhrase: string) => {
    try {
      // Validate seed phrase
      if (!bip39.validateMnemonic(seedPhrase)) {
        throw new Error("Invalid seed phrase");
      }

      // Derive keypair from seed phrase
      const seed = await bip39.mnemonicToSeed(seedPhrase);
      const derivedSeed = derivePath(
        "m/44'/501'/0'/0'",
        seed.toString("hex"),
      ).key;
      const keypair = Keypair.fromSeed(derivedSeed);

      const privateKeyArray = Array.from(keypair.secretKey);
      const privateKeyString = JSON.stringify(privateKeyArray);

      // Save to localStorage
      localStorage.setItem("custom_wallet_seed_phrase", seedPhrase);
      localStorage.setItem("custom_wallet_private_key", privateKeyString);

      setWalletState({
        connected: true,
        address: keypair.publicKey.toString(),
        balance: 0,
        seedPhrase: seedPhrase,
        privateKey: privateKeyString,
        keypair: keypair,
      });

      await getBalance();
      console.log("Connected with seed phrase:", keypair.publicKey.toString());
    } catch (error) {
      console.error("Failed to connect with seed phrase:", error);
      throw error;
    }
  };

  const getBalance = async (): Promise<number> => {
    if (!walletState.keypair) return 0;

    // In demo mode, just return the current balance from localStorage
    const DEMO_MODE = true;

    if (DEMO_MODE) {
      const savedBalance = localStorage.getItem("custom_wallet_balance");
      const currentBalance = savedBalance
        ? parseFloat(savedBalance)
        : walletState.balance;

      console.log(`Demo mode: Current balance is ${currentBalance} SOL`);
      setWalletState((prev) => ({ ...prev, balance: currentBalance }));
      return currentBalance;
    } else {
      // Real blockchain balance (for production)
      try {
        const balance = await connection.getBalance(
          walletState.keypair.publicKey,
        );
        const solBalance = balance / LAMPORTS_PER_SOL;

        setWalletState((prev) => ({ ...prev, balance: solBalance }));
        localStorage.setItem("custom_wallet_balance", solBalance.toString());
        return solBalance;
      } catch (error) {
        console.error("Failed to get balance:", error);
        return 0;
      }
    }
  };

  const sendGameTransaction = async (
    type: "stake" | "payout",
    amount: number,
    recipientAddress?: string,
  ): Promise<string> => {
    if (!walletState.keypair) {
      throw new Error("Wallet not connected");
    }

    try {
      // For demo purposes, simulate transactions to avoid devnet issues
      const DEMO_MODE = true;

      if (DEMO_MODE) {
        // Simulate transaction without actual blockchain interaction
        console.log(`Simulating ${type} transaction for ${amount} SOL`);

        if (type === "stake") {
          // Deduct from wallet balance
          const newBalance = Math.max(0, walletState.balance - amount);
          setWalletState((prev) => ({
            ...prev,
            balance: newBalance,
          }));
          localStorage.setItem("custom_wallet_balance", newBalance.toString());
        } else if (type === "payout" && recipientAddress) {
          // Add to wallet balance (if this wallet is the recipient)
          if (recipientAddress === walletState.address) {
            const newBalance = walletState.balance + amount;
            setWalletState((prev) => ({
              ...prev,
              balance: newBalance,
            }));
            localStorage.setItem(
              "custom_wallet_balance",
              newBalance.toString(),
            );
            console.log(
              `Payout received: +${amount} SOL. New balance: ${newBalance} SOL`,
            );
            console.log(
              `Balance updated from ${walletState.balance} to ${newBalance}`,
            );
          } else {
            console.log(`Payout sent to ${recipientAddress}: ${amount} SOL`);
          }
        }

        // Return fake transaction signature
        const fakeSignature = `demo_${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log(`${type} transaction simulated:`, fakeSignature);
        return fakeSignature;
      } else {
        // Real blockchain transaction (for production)
        const transaction = new Transaction();

        if (type === "stake") {
          transaction.add(
            SystemProgram.transfer({
              fromPubkey: walletState.keypair.publicKey,
              toPubkey: new PublicKey(GAME_CONFIG.DEVELOPER_WALLET),
              lamports: amount * LAMPORTS_PER_SOL,
            }),
          );
        } else if (type === "payout" && recipientAddress) {
          transaction.add(
            SystemProgram.transfer({
              fromPubkey: new PublicKey(GAME_CONFIG.DEVELOPER_WALLET),
              toPubkey: new PublicKey(recipientAddress),
              lamports: amount * LAMPORTS_PER_SOL,
            }),
          );
        }

        const signature = await sendAndConfirmTransaction(
          connection,
          transaction,
          [walletState.keypair],
          { commitment: "confirmed" },
        );

        console.log(`${type} transaction successful:`, signature);
        await getBalance();
        return signature;
      }
    } catch (error) {
      console.error(`Failed to send ${type} transaction:`, error);
      throw error;
    }
  };

  const disconnect = () => {
    localStorage.removeItem("custom_wallet_private_key");
    localStorage.removeItem("custom_wallet_seed_phrase");
    setWalletState({
      connected: false,
      address: null,
      balance: 0,
      seedPhrase: null,
      privateKey: null,
      keypair: null,
    });
  };

  const exportPrivateKey = (): string | null => {
    return walletState.privateKey;
  };

  const exportSeedPhrase = (): string | null => {
    return walletState.seedPhrase;
  };

  // Request airdrop from Solana devnet faucet
  const requestAirdrop = async (): Promise<boolean> => {
    if (!walletState.keypair) {
      console.error("No wallet connected");
      return false;
    }

    try {
      console.log("Requesting airdrop...");

      // For demo purposes, we'll simulate adding SOL to the wallet
      // This is more reliable than the actual devnet faucet which has rate limits

      // Fake transaction - in a real app, this would be a real airdrop
      // For demo, we'll just update the balance directly
      const newBalance = walletState.balance + 2.0;
      setWalletState((prev) => ({
        ...prev,
        balance: newBalance,
      }));
      localStorage.setItem("custom_wallet_balance", newBalance.toString());

      console.log("Airdrop successful! Added 2 SOL");
      return true;
    } catch (error) {
      console.error("Airdrop failed:", error);
      return false;
    }
  };

  return {
    walletState,
    createWallet,
    connectExistingWallet,
    connectWithSeedPhrase,
    getBalance,
    sendGameTransaction,
    requestAirdrop,
    disconnect,
    exportPrivateKey,
    exportSeedPhrase,
  };
}
