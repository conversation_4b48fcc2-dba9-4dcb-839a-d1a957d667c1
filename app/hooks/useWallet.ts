"use client";

import {
  useWallet as useSolanaWallet,
  useConnection,
} from "@solana/wallet-adapter-react";
import {
  PublicKey,
  Transaction,
  SystemProgram,
  LAMPORTS_PER_SOL,
} from "@solana/web3.js";
import { useState, useCallback, useEffect } from "react";
import {
  GAME_CONFIG,
  calculatePlatformFee,
  calculateTotalStake,
  isDeveloperWalletValid,
} from "../config/game";

export interface WalletState {
  connected: boolean;
  connecting: boolean;
  publicKey: PublicKey | null;
  address: string | null;
  balance: number | null;
}

export interface TransactionResult {
  success: boolean;
  signature?: string;
  error?: string;
}

export function useWallet() {
  const walletContext = useSolanaWallet();
  const {
    publicKey,
    connected,
    connecting,
    connect,
    disconnect,
    sendTransaction,
    wallet,
    wallets,
    select,
  } = walletContext;

  const { connection } = useConnection();
  const [balance, setBalance] = useState<number | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Auto-select Phantom wallet if available and no wallet is selected
  useEffect(() => {
    if (!wallet && wallets.length > 0 && select) {
      const phantomWallet = wallets.find((w) => w.adapter.name === "Phantom");
      if (phantomWallet) {
        console.log("Auto-selecting Phantom wallet");
        select(phantomWallet.adapter.name);
      }
    }
  }, [wallet, wallets, select]);

  // Debug wallet context
  console.log("useWallet hook - wallet context:", {
    wallet: wallet?.adapter?.name,
    connected,
    connecting,
    publicKey: publicKey?.toString(),
    hasConnect: !!connect,
    hasSelect: !!select,
    walletsCount: wallets.length,
    contextKeys: Object.keys(walletContext),
  });

  // Get wallet state
  const walletState: WalletState = {
    connected,
    connecting,
    publicKey,
    address: publicKey?.toBase58() || null,
    balance,
  };

  // Check if Phantom is available
  const isPhantomAvailable = useCallback(() => {
    if (typeof window === "undefined") return false;
    return !!(window as any).phantom?.solana?.isPhantom;
  }, []);

  // Connect wallet
  const connectWallet = useCallback(async () => {
    try {
      console.log("Connect wallet called, current state:", {
        connected,
        connecting,
        wallet: wallet?.adapter?.name,
        publicKey: publicKey?.toString(),
        phantomAvailable: isPhantomAvailable(),
        wallets: wallets.length,
      });

      if (connecting) {
        console.log("Already connecting, skipping...");
        return false;
      }

      if (connected) {
        console.log("Already connected");
        return true;
      }

      // Check if Phantom is installed
      if (!isPhantomAvailable()) {
        throw new Error(
          "Phantom wallet not detected. Please install Phantom extension and refresh the page.",
        );
      }

      // If no wallet is selected, try to select Phantom
      if (!wallet && wallets.length > 0) {
        const phantomWallet = wallets.find((w) => w.adapter.name === "Phantom");
        if (phantomWallet && select) {
          console.log("Auto-selecting Phantom wallet");
          select(phantomWallet.adapter.name);
          // Wait a bit for the selection to take effect
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      if (!wallet?.adapter) {
        throw new Error(
          "Phantom wallet not available. Please install Phantom extension.",
        );
      }

      console.log("Attempting to connect to:", wallet.adapter.name);
      await connect();
      console.log("Connection successful");

      return true;
    } catch (error: any) {
      console.error("Connection failed:", error);

      // More specific error handling
      if (
        error.message?.includes("User rejected") ||
        error.message?.includes("rejected")
      ) {
        throw new Error("Connection cancelled by user");
      } else if (
        error.message?.includes("not found") ||
        error.message?.includes("not detected")
      ) {
        throw new Error(
          "Phantom wallet not found. Please install Phantom extension.",
        );
      } else if (error.message?.includes("not available")) {
        throw new Error(
          "Phantom wallet not available. Please refresh the page and try again.",
        );
      } else {
        throw new Error(
          error.message || "Failed to connect wallet. Please try again.",
        );
      }
    }
  }, [
    connect,
    wallet,
    connected,
    connecting,
    publicKey,
    wallets,
    select,
    isPhantomAvailable,
  ]);

  // Disconnect wallet
  const disconnectWallet = useCallback(async () => {
    try {
      await disconnect();
      setBalance(null);
    } catch (error) {
      console.error("Failed to disconnect wallet:", error);
    }
  }, [disconnect]);

  // Get wallet balance
  const getBalance = useCallback(async () => {
    if (!publicKey || !connected) return null;

    try {
      const balance = await connection.getBalance(publicKey);
      const solBalance = balance / LAMPORTS_PER_SOL;
      setBalance(solBalance);
      return solBalance;
    } catch (error) {
      console.error("Failed to get balance:", error);
      return null;
    }
  }, [publicKey, connected, connection]);

  // Send SOL transaction for staking
  const sendGameTransaction = useCallback(
    async (
      stakeAmount: number,
      recipient?: PublicKey,
    ): Promise<TransactionResult> => {
      if (!publicKey || !connected) {
        return { success: false, error: "Wallet not connected" };
      }

      if (!isDeveloperWalletValid()) {
        return { success: false, error: "Developer wallet not configured" };
      }

      setIsProcessing(true);

      try {
        const platformFee = calculatePlatformFee(stakeAmount);
        const totalStake = calculateTotalStake(stakeAmount);

        console.log(`Staking for ${stakeAmount} SOL wager:`);
        console.log(`- Wager amount: ${stakeAmount} SOL`);
        console.log(`- Platform fee: ${platformFee} SOL`);
        console.log(`- Total deducted: ${totalStake} SOL`);

        // Create transaction with multiple instructions
        const transaction = new Transaction();

        if (recipient) {
          // Transfer to recipient (for winner payouts)
          transaction.add(
            SystemProgram.transfer({
              fromPubkey: publicKey,
              toPubkey: recipient,
              lamports: stakeAmount * LAMPORTS_PER_SOL,
            }),
          );
        } else {
          // For staking: Send full amount (wager + platform fee) to developer wallet
          // Developer wallet acts as escrow and will pay out winner later
          transaction.add(
            SystemProgram.transfer({
              fromPubkey: publicKey,
              toPubkey: GAME_CONFIG.DEVELOPER_WALLET,
              lamports: totalStake * LAMPORTS_PER_SOL,
            }),
          );
        }

        // Get recent blockhash
        const { blockhash } = await connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = publicKey;

        // Send transaction
        const signature = await sendTransaction(transaction, connection);

        // Wait for confirmation
        await connection.confirmTransaction(signature, "confirmed");

        // Update balance after transaction
        await getBalance();

        return { success: true, signature };
      } catch (error) {
        console.error("Transaction failed:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Transaction failed",
        };
      } finally {
        setIsProcessing(false);
      }
    },
    [publicKey, connected, connection, sendTransaction, getBalance],
  );

  // Send winner payout
  const sendWinnerPayout = useCallback(
    async (
      winnerWallet: PublicKey,
      wagerAmount: number,
    ): Promise<TransactionResult> => {
      if (!publicKey || !connected) {
        return { success: false, error: "Wallet not connected" };
      }

      setIsProcessing(true);

      try {
        const winnerPayout = wagerAmount * 2; // Winner gets both wagers

        console.log(`Paying out winner:`);
        console.log(`- Wager amount: ${wagerAmount} SOL each`);
        console.log(`- Winner receives: ${winnerPayout} SOL (both wagers)`);
        console.log(`- Paying from developer wallet (escrow) to winner`);

        const transaction = new Transaction();

        // Send winnings to winner from developer wallet (which holds the escrow)
        // Note: In production, this would be signed by the developer wallet, not the player
        // For demo purposes, we're simulating the payout
        transaction.add(
          SystemProgram.transfer({
            fromPubkey: publicKey, // In production: GAME_CONFIG.DEVELOPER_WALLET
            toPubkey: winnerWallet,
            lamports: winnerPayout * LAMPORTS_PER_SOL,
          }),
        );

        // Get recent blockhash
        const { blockhash } = await connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = publicKey;

        // Send transaction
        const signature = await sendTransaction(transaction, connection);

        // Wait for confirmation
        await connection.confirmTransaction(
          signature,
          GAME_CONFIG.CONFIRMATION_LEVEL,
        );

        // Update balance after transaction
        await getBalance();

        return { success: true, signature };
      } catch (error) {
        console.error("Winner payout failed:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "Payout failed",
        };
      } finally {
        setIsProcessing(false);
      }
    },
    [publicKey, connected, connection, sendTransaction, getBalance],
  );

  return {
    walletState,
    connectWallet,
    disconnectWallet,
    getBalance,
    sendGameTransaction,
    sendWinnerPayout,
    isProcessing,
  };
}
