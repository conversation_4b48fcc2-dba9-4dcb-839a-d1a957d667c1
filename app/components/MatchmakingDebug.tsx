"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import { useWallet } from "../hooks/useWallet";

export default function MatchmakingDebug() {
  const { walletState } = useWallet();
  const convexMatchmaking = useConvexMatchmaking();

  const waitingCounts = convexMatchmaking.useWaitingCounts() || {};
  const matchResult = convexMatchmaking.useMatchCheck(
    walletState.address || "",
  );

  const [isVisible, setIsVisible] = useState(false);
  const [testResult, setTestResult] = useState<string>("");

  const testJoinQueue = async () => {
    if (!walletState.address) {
      setTestResult("No wallet connected");
      return;
    }

    try {
      setTestResult("Testing join queue...");
      const result = await convexMatchmaking.joinQueue(
        walletState.address,
        0.001,
      );
      setTestResult(`Join result: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      setTestResult(`Error: ${error}`);
    }
  };

  const testLeaveQueue = async () => {
    if (!walletState.address) {
      setTestResult("No wallet connected");
      return;
    }

    try {
      setTestResult("Testing leave queue...");
      await convexMatchmaking.leaveQueue(walletState.address);
      setTestResult("Left queue successfully");
    } catch (error) {
      setTestResult(`Error: ${error}`);
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm z-50">
        Show Debug
      </button>
    );
  }

  return (
    <motion.div
      className="fixed bottom-4 right-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50 max-w-md"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}>
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-foreground font-semibold">🔍 Convex Debug</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-muted-foreground hover:text-foreground">
          ✕
        </button>
      </div>

      <div className="space-y-3 text-sm">
        <div>
          <div className="text-muted-foreground mb-2">Waiting Counts:</div>
          <div className="space-y-1 ml-4">
            {Object.entries(waitingCounts).map(([wager, count]) => (
              <div
                key={wager}
                className="text-xs text-foreground bg-muted/50 rounded px-2 py-1">
                {wager} SOL: {count} waiting
              </div>
            ))}
            {Object.keys(waitingCounts).length === 0 && (
              <div className="text-xs text-muted-foreground">
                No players waiting
              </div>
            )}
          </div>
        </div>

        <div>
          <div className="text-muted-foreground mb-2">Match Status:</div>
          <div className="text-xs text-foreground bg-muted/50 rounded px-2 py-1 max-h-20 overflow-y-auto">
            {matchResult
              ? JSON.stringify(matchResult, null, 2)
              : "No match data"}
          </div>
        </div>

        <div>
          <div className="text-muted-foreground mb-2">Wallet:</div>
          <div className="text-xs text-foreground bg-muted/50 rounded px-2 py-1">
            {walletState.address
              ? `${walletState.address.slice(0, 8)}...${walletState.address.slice(-4)}`
              : "Not connected"}
          </div>
        </div>

        <div className="flex gap-2">
          <button
            onClick={testJoinQueue}
            className="bg-green-500 text-white px-2 py-1 rounded text-xs">
            Test Join
          </button>
          <button
            onClick={testLeaveQueue}
            className="bg-red-500 text-white px-2 py-1 rounded text-xs">
            Test Leave
          </button>
        </div>

        {testResult && (
          <div className="text-xs text-foreground bg-muted/50 rounded px-2 py-1 whitespace-pre-wrap max-h-32 overflow-y-auto">
            {testResult}
          </div>
        )}

        <div className="text-xs text-muted-foreground pt-2 border-t border-border">
          Real-time Convex data • Click ✕ to close
        </div>
      </div>
    </motion.div>
  );
}
