"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import GameStartScreen from "./GameStartScreen";
import Header from "./Header";
import Footer from "./Footer";
import { useWallet } from "../hooks/useWallet";

export type GameState = "start" | "playing" | "gameOver";
export type Player = 1 | 2;
export type CellState = 0 | 1 | 2; // 0 = empty, 1 = player 1, 2 = player 2

export interface GameData {
  board: CellState[][];
  currentPlayer: Player;
  winner: Player | null;
  isDraw: boolean;
  wagerAmount: number;
  totalMoves: number;
  gameMode: "local" | "online"; // For now, we'll focus on local play
}

interface DroppingCoin {
  id: string;
  column: number;
  player: Player;
  targetRow: number;
  startTime: number;
}

const ROWS = 6;
const COLS = 7;
const CELL_SIZE = 60; // Size of each cell in pixels
const DROP_DURATION = 800; // Animation duration in milliseconds

const initialBoard: CellState[][] = Array(ROWS)
  .fill(null)
  .map(() => Array(COLS).fill(0));

const initialGameData: GameData = {
  board: initialBoard,
  currentPlayer: 1,
  winner: null,
  isDraw: false,
  wagerAmount: 0.001,
  totalMoves: 0,
  gameMode: "local",
};

export default function Connect4Game() {
  const [gameState, setGameState] = useState<GameState>("start");
  const [gameData, setGameData] = useState<GameData>(initialGameData);
  const [droppingCoins, setDroppingCoins] = useState<DroppingCoin[]>([]);
  const [hoveredColumn, setHoveredColumn] = useState<number | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);
  const [winningCells, setWinningCells] = useState<
    { row: number; col: number }[]
  >([]);

  const { walletState } = useWallet();

  // Start a new game
  const startGame = (wagerAmount: number) => {
    setGameData({
      ...initialGameData,
      wagerAmount,
    });
    setGameState("playing");
    setDroppingCoins([]);
    setWinningCells([]);
    setShowResultModal(false);
  };

  // Check if a column is full
  const isColumnFull = (column: number): boolean => {
    return gameData.board[0][column] !== 0;
  };

  // Find the target row for a dropped coin
  const findTargetRow = (column: number): number => {
    for (let row = ROWS - 1; row >= 0; row--) {
      if (gameData.board[row][column] === 0) {
        return row;
      }
    }
    return -1; // Column is full
  };

  // Check for winning condition
  const checkWinner = (
    board: CellState[][],
    lastRow: number,
    lastCol: number,
    player: Player,
  ): { row: number; col: number }[] | null => {
    const directions = [
      [0, 1], // horizontal
      [1, 0], // vertical
      [1, 1], // diagonal \
      [1, -1], // diagonal /
    ];

    for (const [deltaRow, deltaCol] of directions) {
      const cells: { row: number; col: number }[] = [
        { row: lastRow, col: lastCol },
      ];

      // Check in positive direction
      for (let i = 1; i < 4; i++) {
        const newRow = lastRow + deltaRow * i;
        const newCol = lastCol + deltaCol * i;
        if (
          newRow >= 0 &&
          newRow < ROWS &&
          newCol >= 0 &&
          newCol < COLS &&
          board[newRow][newCol] === player
        ) {
          cells.push({ row: newRow, col: newCol });
        } else {
          break;
        }
      }

      // Check in negative direction
      for (let i = 1; i < 4; i++) {
        const newRow = lastRow - deltaRow * i;
        const newCol = lastCol - deltaCol * i;
        if (
          newRow >= 0 &&
          newRow < ROWS &&
          newCol >= 0 &&
          newCol < COLS &&
          board[newRow][newCol] === player
        ) {
          cells.unshift({ row: newRow, col: newCol });
        } else {
          break;
        }
      }

      if (cells.length >= 4) {
        return cells.slice(0, 4); // Return first 4 cells
      }
    }

    return null;
  };

  // Check for draw
  const checkDraw = (board: CellState[][]): boolean => {
    return board[0].every((cell) => cell !== 0);
  };

  // Handle coin drop with smooth animation
  const handleColumnClick = (column: number) => {
    if (gameData.winner || gameData.isDraw || isColumnFull(column)) {
      return;
    }

    const targetRow = findTargetRow(column);
    if (targetRow === -1) return;

    // Create dropping coin animation
    const droppingCoin: DroppingCoin = {
      id: `${Date.now()}-${column}`,
      column,
      player: gameData.currentPlayer,
      targetRow,
      startTime: Date.now(),
    };

    setDroppingCoins((prev) => [...prev, droppingCoin]);

    // After animation completes, update the board
    setTimeout(() => {
      setGameData((prevData) => {
        const newBoard = prevData.board.map((row) => [...row]);
        newBoard[targetRow][column] = prevData.currentPlayer;

        // Check for winner
        const winningCells = checkWinner(
          newBoard,
          targetRow,
          column,
          prevData.currentPlayer,
        );
        if (winningCells) {
          setWinningCells(winningCells);
          return {
            ...prevData,
            board: newBoard,
            winner: prevData.currentPlayer,
            totalMoves: prevData.totalMoves + 1,
          };
        }

        // Check for draw
        if (checkDraw(newBoard)) {
          return {
            ...prevData,
            board: newBoard,
            isDraw: true,
            totalMoves: prevData.totalMoves + 1,
          };
        }

        // Switch players
        return {
          ...prevData,
          board: newBoard,
          currentPlayer: prevData.currentPlayer === 1 ? 2 : 1,
          totalMoves: prevData.totalMoves + 1,
        };
      });

      // Remove the dropping coin after animation
      setDroppingCoins((prev) =>
        prev.filter((coin) => coin.id !== droppingCoin.id),
      );
    }, DROP_DURATION);
  };

  // Show result modal when game ends
  useEffect(() => {
    if (gameData.winner || gameData.isDraw) {
      setTimeout(() => setShowResultModal(true), 1000);
    }
  }, [gameData.winner, gameData.isDraw]);

  // Reset game to start screen
  const resetGame = () => {
    setGameData(initialGameData);
    setGameState("start");
    setShowResultModal(false);
    setDroppingCoins([]);
    setWinningCells([]);
  };

  // Handle result modal actions
  const handleModalClose = () => {
    setShowResultModal(false);
    resetGame();
  };

  const handleRestart = () => {
    setShowResultModal(false);
    const currentWager = gameData.wagerAmount;
    resetGame();
    setTimeout(() => {
      startGame(currentWager);
    }, 100);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 pt-20">
        {gameState === "start" && <GameStartScreen onStartGame={startGame} />}

        {gameState === "playing" && (
          <div className="max-w-4xl mx-auto px-4 py-8">
            {/* Game Header */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Connect 4 Game
              </h1>
              <div className="flex items-center justify-center gap-6 text-muted-foreground">
                <div>Pot: {(gameData.wagerAmount * 2).toFixed(3)} SOL</div>
                <div>Move: {gameData.totalMoves + 1}</div>
                <div
                  className={`font-semibold ${
                    gameData.currentPlayer === 1
                      ? "text-red-500"
                      : "text-yellow-500"
                  }`}>
                  {gameData.currentPlayer === 1 ? "Red" : "Yellow"} Player's
                  Turn
                </div>
              </div>
            </motion.div>

            {/* Game Board Container */}
            <motion.div
              className="flex flex-col items-center"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}>
              {/* Column Hover Indicators */}
              <div className="grid grid-cols-7 gap-2 mb-4">
                {Array.from({ length: COLS }, (_, col) => (
                  <motion.div
                    key={col}
                    className="flex flex-col items-center cursor-pointer"
                    onMouseEnter={() => setHoveredColumn(col)}
                    onMouseLeave={() => setHoveredColumn(null)}
                    onClick={() => handleColumnClick(col)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}>
                    <motion.div
                      className={`w-12 h-12 rounded-full mb-2 border-2 border-dashed transition-all duration-200 ${
                        hoveredColumn === col
                          ? gameData.currentPlayer === 1
                            ? "border-red-500 bg-red-500/20"
                            : "border-yellow-500 bg-yellow-500/20"
                          : "border-muted-foreground/30"
                      }`}
                      animate={{
                        scale: hoveredColumn === col ? 1.1 : 1,
                        y: hoveredColumn === col ? -2 : 0,
                      }}
                      transition={{ duration: 0.2 }}>
                      {hoveredColumn === col && (
                        <motion.div
                          className={`w-full h-full rounded-full ${
                            gameData.currentPlayer === 1
                              ? "bg-red-500"
                              : "bg-yellow-500"
                          }`}
                          initial={{ scale: 0 }}
                          animate={{ scale: 0.8 }}
                          transition={{ duration: 0.2 }}
                        />
                      )}
                    </motion.div>
                    <motion.div
                      className="text-xs text-muted-foreground"
                      animate={{ opacity: hoveredColumn === col ? 1 : 0.5 }}>
                      ↓
                    </motion.div>
                  </motion.div>
                ))}
              </div>

              {/* Game Board */}
              <motion.div
                className="relative bg-blue-600 p-4 rounded-2xl shadow-2xl"
                style={{
                  width: COLS * (CELL_SIZE + 8) + 16,
                  height: ROWS * (CELL_SIZE + 8) + 16,
                }}>
                {/* Board Grid */}
                <div className="grid grid-rows-6 grid-cols-7 gap-2">
                  {gameData.board.map((row, rowIndex) =>
                    row.map((cell, colIndex) => {
                      const isWinning = winningCells.some(
                        (winCell) =>
                          winCell.row === rowIndex && winCell.col === colIndex,
                      );

                      return (
                        <motion.div
                          key={`${rowIndex}-${colIndex}`}
                          className="relative"
                          style={{ width: CELL_SIZE, height: CELL_SIZE }}>
                          {/* Cell Background */}
                          <div className="absolute inset-0 bg-blue-800 rounded-full border-2 border-blue-700" />

                          {/* Placed Coin */}
                          {cell !== 0 && (
                            <motion.div
                              className={`absolute inset-1 rounded-full shadow-lg ${
                                cell === 1 ? "bg-red-500" : "bg-yellow-500"
                              } ${isWinning ? "ring-4 ring-white" : ""}`}
                              initial={{ scale: 0 }}
                              animate={{
                                scale: 1,
                                boxShadow: isWinning
                                  ? "0 0 20px rgba(255,255,255,0.8)"
                                  : "0 4px 8px rgba(0,0,0,0.3)",
                              }}
                              transition={{
                                duration: 0.3,
                                type: "spring",
                                stiffness: 300,
                                damping: 20,
                              }}
                            />
                          )}
                        </motion.div>
                      );
                    }),
                  )}
                </div>

                {/* Dropping Coins Animation */}
                <AnimatePresence>
                  {droppingCoins.map((coin) => (
                    <motion.div
                      key={coin.id}
                      className={`absolute rounded-full shadow-lg ${
                        coin.player === 1 ? "bg-red-500" : "bg-yellow-500"
                      }`}
                      style={{
                        width: CELL_SIZE - 8,
                        height: CELL_SIZE - 8,
                        left: coin.column * (CELL_SIZE + 8) + 20,
                        top: 20,
                      }}
                      initial={{ y: -100 }}
                      animate={{
                        y: coin.targetRow * (CELL_SIZE + 8) + 20,
                      }}
                      transition={{
                        duration: DROP_DURATION / 1000,
                        type: "spring",
                        stiffness: 100,
                        damping: 15,
                        bounce: 0.3,
                      }}
                      exit={{ scale: 0, opacity: 0 }}
                    />
                  ))}
                </AnimatePresence>
              </motion.div>

              {/* Game Controls */}
              <motion.div
                className="mt-8 flex gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}>
                <button
                  onClick={resetGame}
                  className="px-6 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/80 transition-colors">
                  New Game
                </button>
              </motion.div>
            </motion.div>
          </div>
        )}

        {/* Simple Result Modal */}
        <AnimatePresence>
          {showResultModal && (
            <motion.div
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}>
              <motion.div
                className="bg-card rounded-lg p-8 border border-border max-w-md w-full mx-4"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}>
                <div className="text-center">
                  <div className="text-6xl mb-4">
                    {gameData.winner === 1
                      ? "🔴"
                      : gameData.winner === 2
                        ? "🟡"
                        : "🤝"}
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-2">
                    {gameData.winner
                      ? `${gameData.winner === 1 ? "Red" : "Yellow"} Player Wins!`
                      : "It's a Draw!"}
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    {gameData.winner
                      ? `Congratulations! You won ${(gameData.wagerAmount * 2).toFixed(3)} SOL!`
                      : "Good game! No winner this time."}
                  </p>
                  <div className="flex gap-4">
                    <button
                      onClick={handleRestart}
                      className="flex-1 py-3 px-6 btn-gradient rounded-lg font-semibold">
                      Play Again
                    </button>
                    <button
                      onClick={handleModalClose}
                      className="flex-1 py-3 px-6 bg-muted text-foreground rounded-lg hover:bg-muted/80 transition-colors">
                      Back to Menu
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <Footer />
    </div>
  );
}
