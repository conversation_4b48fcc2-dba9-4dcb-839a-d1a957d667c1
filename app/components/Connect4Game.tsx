"use client";

import { useState, useEffect } from "react";
import GameStartScreen from "./GameStartScreen";
import GameplayScreen from "./GameplayScreen";
import GameResultModal from "./GameResultModal";
import PlayerSearchScreen from "./PlayerSearchScreen";
import Header from "./Header";
import Footer from "./Footer";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import { Id } from "../../convex/_generated/dataModel";
import { useWallet } from "../hooks/useWallet";

export type GameState = "start" | "searching" | "playing" | "gameOver";
export type Player = 1 | 2;
export type CellState = 0 | 1 | 2; // 0 = empty, 1 = player 1, 2 = player 2

export interface GameData {
  board: CellState[][];
  currentPlayer: Player;
  winner: Player | null;
  isDraw: boolean;
  player1Wagered: number;
  player2Wagered: number;
  wagerAmount: number;
  player1Wallet: string;
  player2Wallet: string;
  timeLeft: number;
  totalMoves: number;
  turnStartTime?: number;
  turnTimeLimit?: number;
}

const initialBoard: CellState[][] = Array(6)
  .fill(null)
  .map(() => Array(7).fill(0));

const initialGameData: GameData = {
  board: initialBoard,
  currentPlayer: 1,
  winner: null,
  isDraw: false,
  player1Wagered: 0,
  player2Wagered: 0,
  wagerAmount: 0.001,
  player1Wallet: "5zr..GvH",
  player2Wallet: "9ab..PqR",
  timeLeft: 10,
  totalMoves: 0,
  turnStartTime: Date.now(),
  turnTimeLimit: 10000,
};

export default function Connect4Game() {
  const [gameState, setGameState] = useState<GameState>("start");
  const [gameData, setGameData] = useState<GameData>(initialGameData);
  const [currentGameId, setCurrentGameId] = useState<Id<"gameSessions"> | null>(
    null,
  );
  const [showResultModal, setShowResultModal] = useState(false);
  const [isMovePending, setIsMovePending] = useState(false);

  const { walletState } = useWallet();
  const convexMatchmaking = useConvexMatchmaking();
  const convexGameState = convexMatchmaking.useGameState(
    currentGameId || undefined,
  );

  // Load game state from localStorage on mount
  useEffect(() => {
    const savedGameState = localStorage.getItem("connect4_game_state");
    const savedGameId = localStorage.getItem("connect4_game_id");

    if (savedGameState && savedGameId) {
      setGameState(savedGameState as GameState);
      setCurrentGameId(savedGameId as Id<"gameSessions">);
    }
  }, []);

  // Save game state to localStorage when it changes
  useEffect(() => {
    if (gameState !== "start") {
      localStorage.setItem("connect4_game_state", gameState);
    } else {
      localStorage.removeItem("connect4_game_state");
    }
  }, [gameState]);

  useEffect(() => {
    if (currentGameId) {
      localStorage.setItem("connect4_game_id", currentGameId);
    } else {
      localStorage.removeItem("connect4_game_id");
    }
  }, [currentGameId]);

  // Determine which player the current user is
  const getCurrentUserPlayer = (): 1 | 2 => {
    if (!walletState.address || !convexGameState) return 1;

    if (convexGameState.player1Wallet === walletState.address) {
      console.log(`Current user is Player 1. Wallet: ${walletState.address}`);
      return 1;
    } else if (convexGameState.player2Wallet === walletState.address) {
      console.log(`Current user is Player 2. Wallet: ${walletState.address}`);
      return 2;
    }
    console.log(`Default to Player 1. Wallet: ${walletState.address}`);
    return 1; // Default to player 1
  };

  // Sync Convex game state with local game data
  useEffect(() => {
    if (convexGameState && currentGameId) {
      setGameData((prev) => ({
        ...prev,
        board: convexGameState.board,
        currentPlayer: convexGameState.currentPlayer,
        totalMoves: convexGameState.totalMoves,
        winner: convexGameState.winner,
        isDraw: convexGameState.isDraw || false,
        player1Wallet: convexGameState.player1Wallet,
        player2Wallet: convexGameState.player2Wallet,
        wagerAmount: convexGameState.wagerAmount,
        player1Wagered: convexGameState.wagerAmount,
        player2Wagered: convexGameState.wagerAmount,
        turnStartTime: convexGameState.turnStartTime,
        turnTimeLimit: convexGameState.turnTimeLimit,
      }));

      // Update game state based on Convex state
      if (
        convexGameState.gameState === "finished" &&
        (convexGameState.winner || convexGameState.isDraw)
      ) {
        // Show result modal instead of changing game state
        setShowResultModal(true);
      } else if (convexGameState.gameState === "playing") {
        setGameState("playing");
      }
    }
  }, [convexGameState, currentGameId]);

  const startGame = (wagerAmount: number) => {
    const now = Date.now();
    setGameData({
      ...initialGameData,
      wagerAmount,
      turnStartTime: now,
      turnTimeLimit: 10000,
    });
    setGameState("searching");
    console.log("Game started with timer data:", {
      turnStartTime: now,
      turnTimeLimit: 10000,
    });
  };

  const onPlayerFound = (playerWallet: string, gameId?: Id<"gameSessions">) => {
    if (gameId) {
      setCurrentGameId(gameId);
    }

    const now = Date.now();
    setGameData((prev) => ({
      ...prev,
      player2Wallet: playerWallet,
      // Set wagered amounts once when both players join
      player1Wagered: prev.wagerAmount,
      player2Wagered: prev.wagerAmount,
      // Ensure timer data is set when game starts
      turnStartTime: now,
      turnTimeLimit: 10000,
    }));
    setGameState("playing");
    console.log("Player found, game starting with timer:", {
      turnStartTime: now,
    });
  };

  const cancelSearch = () => {
    setGameState("start");
  };

  const makeMove = async (column: number) => {
    if (!currentGameId || !walletState.address) return;
    if (gameData.winner || gameData.isDraw) return;
    if (isMovePending) {
      console.log("Move blocked - another move is pending");
      return;
    }

    try {
      setIsMovePending(true);
      console.log(`Making move in column ${column}`);

      // Make move through Convex (this will sync to all players)
      const result = await convexMatchmaking.makeMove(
        currentGameId,
        walletState.address,
        column,
      );

      console.log("Move result:", result);

      // Reset pending state after a short delay
      setTimeout(() => {
        setIsMovePending(false);
      }, 1000);

      // Game state will be updated automatically through the useEffect
      // that watches convexGameState
    } catch (error) {
      console.error("Failed to make move:", error);
      setIsMovePending(false);
    }
  };

  const checkWinner = (board: CellState[][]): Player | null => {
    // Check horizontal, vertical, and diagonal wins
    for (let row = 0; row < 6; row++) {
      for (let col = 0; col < 7; col++) {
        const player = board[row][col];
        if (player === 0) continue;

        // Check horizontal
        if (
          col <= 3 &&
          board[row][col + 1] === player &&
          board[row][col + 2] === player &&
          board[row][col + 3] === player
        ) {
          return player;
        }

        // Check vertical
        if (
          row <= 2 &&
          board[row + 1][col] === player &&
          board[row + 2][col] === player &&
          board[row + 3][col] === player
        ) {
          return player;
        }

        // Check diagonal (top-left to bottom-right)
        if (
          row <= 2 &&
          col <= 3 &&
          board[row + 1][col + 1] === player &&
          board[row + 2][col + 2] === player &&
          board[row + 3][col + 3] === player
        ) {
          return player;
        }

        // Check diagonal (top-right to bottom-left)
        if (
          row <= 2 &&
          col >= 3 &&
          board[row + 1][col - 1] === player &&
          board[row + 2][col - 2] === player &&
          board[row + 3][col - 3] === player
        ) {
          return player;
        }
      }
    }
    return null;
  };

  const resetGame = () => {
    setGameData(initialGameData);
    setGameState("start");
    setCurrentGameId(null);
    setShowResultModal(false);
    setIsMovePending(false);
    // Clear localStorage
    localStorage.removeItem("connect4_game_state");
    localStorage.removeItem("connect4_game_id");
  };

  const handleModalClose = () => {
    setShowResultModal(false);
    resetGame();
  };

  const handleRestart = () => {
    setShowResultModal(false);
    // Keep the same wager amount and start a new game
    const currentWager = gameData.wagerAmount;
    resetGame();
    setTimeout(() => {
      startGame(currentWager);
    }, 100);
  };

  const rematch = () => {
    setGameData({
      ...initialGameData,
      wagerAmount: gameData.wagerAmount,
      player1Wallet: gameData.player1Wallet,
      player2Wallet: gameData.player2Wallet,
    });
    setGameState("playing");
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 pt-20">
        {gameState === "start" && (
          <div>
            <GameStartScreen onStartGame={startGame} />
          </div>
        )}
        {gameState === "searching" && (
          <div>
            <PlayerSearchScreen
              onPlayerFound={onPlayerFound}
              onCancel={cancelSearch}
              wagerAmount={gameData.wagerAmount}
            />
          </div>
        )}
        {gameState === "playing" && (
          <div>
            <GameplayScreen
              gameData={gameData}
              onMakeMove={makeMove}
              currentUserPlayer={getCurrentUserPlayer()}
              onTimeExpired={async () => {
                // Only trigger auto-drop if it's the current user's turn AND wallet matches
                const currentUserPlayer = getCurrentUserPlayer();
                const isMyTurn = gameData.currentPlayer === currentUserPlayer;
                const isMyWallet =
                  walletState.address &&
                  ((currentUserPlayer === 1 &&
                    convexGameState?.player1Wallet === walletState.address) ||
                    (currentUserPlayer === 2 &&
                      convexGameState?.player2Wallet === walletState.address));

                console.log(`Timer expired check:`, {
                  currentPlayer: gameData.currentPlayer,
                  currentUserPlayer,
                  isMyTurn,
                  isMyWallet,
                  myAddress: walletState.address,
                  player1Wallet: convexGameState?.player1Wallet,
                  player2Wallet: convexGameState?.player2Wallet,
                });

                if (isMyTurn && isMyWallet && currentGameId) {
                  console.log("Timer expired - triggering server auto-drop");
                  try {
                    const result =
                      await convexMatchmaking.autoDropOnTimeout(currentGameId);
                    console.log("Server auto-drop result:", result);
                  } catch (error) {
                    console.error("Server auto-drop failed:", error);
                  }
                } else {
                  console.log(
                    "Timer expired - but not my turn or not my wallet, ignoring",
                  );
                }
              }}
            />
          </div>
        )}

        {/* Game Result Modal */}
        <GameResultModal
          gameData={gameData}
          currentUserPlayer={getCurrentUserPlayer()}
          isVisible={showResultModal}
          onClose={handleModalClose}
          onRestart={handleRestart}
        />
      </div>
      <Footer />
    </div>
  );
}
