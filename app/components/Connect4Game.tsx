"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import GameStartScreen from "./GameStartScreen";
import PlayerSearchScreen from "./PlayerSearchScreen";
import GameResultModal from "./GameResultModal";
import Header from "./Header";
import Footer from "./Footer";
import { useWallet } from "../hooks/useWallet";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import { Id } from "../../convex/_generated/dataModel";

export type GameState = "start" | "searching" | "playing" | "gameOver";
export type Player = 1 | 2;
export type CellState = 0 | 1 | 2; // 0 = empty, 1 = player 1, 2 = player 2

export interface GameData {
  board: CellState[][];
  currentPlayer: Player;
  winner: Player | null;
  isDraw: boolean;
  player1Wagered: number;
  player2Wagered: number;
  wagerAmount: number;
  player1Wallet: string;
  player2Wallet: string;
  timeLeft: number;
  totalMoves: number;
  turnStartTime?: number;
  turnTimeLimit?: number;
  gameMode: "local" | "online";
}

interface DroppingCoin {
  id: string;
  column: number;
  player: Player;
  targetRow: number;
  startTime: number;
}

const ROWS = 6;
const COLS = 7;
const CELL_SIZE = 60; // Size of each cell in pixels
const DROP_DURATION = 800; // Animation duration in milliseconds

const initialBoard: CellState[][] = Array(ROWS)
  .fill(null)
  .map(() => Array(COLS).fill(0));

const initialGameData: GameData = {
  board: initialBoard,
  currentPlayer: 1,
  winner: null,
  isDraw: false,
  player1Wagered: 0,
  player2Wagered: 0,
  wagerAmount: 0.001,
  player1Wallet: "",
  player2Wallet: "",
  timeLeft: 10,
  totalMoves: 0,
  turnStartTime: Date.now(),
  turnTimeLimit: 10000,
  gameMode: "online",
};

export default function Connect4Game() {
  const [gameState, setGameState] = useState<GameState>("start");
  const [gameData, setGameData] = useState<GameData>(initialGameData);
  const [currentGameId, setCurrentGameId] = useState<Id<"gameSessions"> | null>(
    null,
  );
  const [droppingCoins, setDroppingCoins] = useState<DroppingCoin[]>([]);
  const [hoveredColumn, setHoveredColumn] = useState<number | null>(null);
  const [showResultModal, setShowResultModal] = useState(false);
  const [winningCells, setWinningCells] = useState<
    { row: number; col: number }[]
  >([]);
  const [isMovePending, setIsMovePending] = useState(false);

  const { walletState } = useWallet();
  const convexMatchmaking = useConvexMatchmaking();
  const convexGameState = convexMatchmaking.useGameState(
    currentGameId || undefined,
  );

  // Start a new game
  const startGame = (wagerAmount: number) => {
    const now = Date.now();
    setGameData({
      ...initialGameData,
      wagerAmount,
      player1Wallet: walletState.address || "",
      turnStartTime: now,
      turnTimeLimit: 10000,
    });
    setGameState("searching");
    setDroppingCoins([]);
    setWinningCells([]);
    setShowResultModal(false);
    console.log("Game started with timer data:", {
      turnStartTime: now,
      turnTimeLimit: 10000,
    });
  };

  // Handle player found
  const onPlayerFound = (playerWallet: string, gameId?: Id<"gameSessions">) => {
    if (gameId) {
      setCurrentGameId(gameId);
    }

    const now = Date.now();
    setGameData((prev) => ({
      ...prev,
      player2Wallet: playerWallet,
      player1Wagered: prev.wagerAmount,
      player2Wagered: prev.wagerAmount,
      turnStartTime: now,
      turnTimeLimit: 10000,
    }));
    setGameState("playing");
    console.log("Player found, game starting with timer:", {
      turnStartTime: now,
    });
  };

  // Cancel search
  const cancelSearch = () => {
    setGameState("start");
  };

  // Determine which player the current user is
  const getCurrentUserPlayer = (): 1 | 2 => {
    if (!walletState.address || !convexGameState) return 1;

    if (convexGameState.player1Wallet === walletState.address) {
      console.log(`Current user is Player 1. Wallet: ${walletState.address}`);
      return 1;
    } else if (convexGameState.player2Wallet === walletState.address) {
      console.log(`Current user is Player 2. Wallet: ${walletState.address}`);
      return 2;
    }
    console.log(`Default to Player 1. Wallet: ${walletState.address}`);
    return 1; // Default to player 1
  };

  // Sync Convex game state with local game data
  useEffect(() => {
    if (convexGameState && currentGameId) {
      setGameData((prev) => ({
        ...prev,
        board: convexGameState.board,
        currentPlayer: convexGameState.currentPlayer,
        totalMoves: convexGameState.totalMoves,
        winner: convexGameState.winner,
        isDraw: convexGameState.isDraw || false,
        player1Wallet: convexGameState.player1Wallet,
        player2Wallet: convexGameState.player2Wallet,
        wagerAmount: convexGameState.wagerAmount,
        player1Wagered: convexGameState.wagerAmount,
        player2Wagered: convexGameState.wagerAmount,
        turnStartTime: convexGameState.turnStartTime,
        turnTimeLimit: convexGameState.turnTimeLimit,
      }));

      // Update game state based on Convex state
      if (
        convexGameState.gameState === "finished" &&
        (convexGameState.winner || convexGameState.isDraw)
      ) {
        // Show result modal instead of changing game state
        setShowResultModal(true);
      } else if (convexGameState.gameState === "playing") {
        setGameState("playing");
      }
    }
  }, [convexGameState, currentGameId]);

  // Check if a column is full
  const isColumnFull = (column: number): boolean => {
    return gameData.board[0][column] !== 0;
  };

  // Find the target row for a dropped coin
  const findTargetRow = (column: number): number => {
    for (let row = ROWS - 1; row >= 0; row--) {
      if (gameData.board[row][column] === 0) {
        return row;
      }
    }
    return -1; // Column is full
  };

  // Check for winning condition
  const checkWinner = (
    board: CellState[][],
    lastRow: number,
    lastCol: number,
    player: Player,
  ): { row: number; col: number }[] | null => {
    const directions = [
      [0, 1], // horizontal
      [1, 0], // vertical
      [1, 1], // diagonal \
      [1, -1], // diagonal /
    ];

    for (const [deltaRow, deltaCol] of directions) {
      const cells: { row: number; col: number }[] = [
        { row: lastRow, col: lastCol },
      ];

      // Check in positive direction
      for (let i = 1; i < 4; i++) {
        const newRow = lastRow + deltaRow * i;
        const newCol = lastCol + deltaCol * i;
        if (
          newRow >= 0 &&
          newRow < ROWS &&
          newCol >= 0 &&
          newCol < COLS &&
          board[newRow][newCol] === player
        ) {
          cells.push({ row: newRow, col: newCol });
        } else {
          break;
        }
      }

      // Check in negative direction
      for (let i = 1; i < 4; i++) {
        const newRow = lastRow - deltaRow * i;
        const newCol = lastCol - deltaCol * i;
        if (
          newRow >= 0 &&
          newRow < ROWS &&
          newCol >= 0 &&
          newCol < COLS &&
          board[newRow][newCol] === player
        ) {
          cells.unshift({ row: newRow, col: newCol });
        } else {
          break;
        }
      }

      if (cells.length >= 4) {
        return cells.slice(0, 4); // Return first 4 cells
      }
    }

    return null;
  };

  // Check for draw
  const checkDraw = (board: CellState[][]): boolean => {
    return board[0].every((cell) => cell !== 0);
  };

  // Handle coin drop with smooth animation for multiplayer
  const handleColumnClick = async (column: number) => {
    if (!currentGameId || !walletState.address) return;
    if (gameData.winner || gameData.isDraw) return;
    if (isMovePending) {
      console.log("Move blocked - another move is pending");
      return;
    }

    // Check if it's the current user's turn
    const currentUserPlayer = getCurrentUserPlayer();
    if (gameData.currentPlayer !== currentUserPlayer) {
      console.log("Not your turn!");
      return;
    }

    // Check if column is full
    if (isColumnFull(column)) return;

    const targetRow = findTargetRow(column);
    if (targetRow === -1) return;

    try {
      setIsMovePending(true);
      console.log(`Making move in column ${column}`);

      // Create dropping coin animation
      const droppingCoin: DroppingCoin = {
        id: `${Date.now()}-${column}`,
        column,
        player: gameData.currentPlayer,
        targetRow,
        startTime: Date.now(),
      };

      setDroppingCoins((prev) => [...prev, droppingCoin]);

      // Make move through Convex (this will sync to all players)
      const result = await convexMatchmaking.makeMove(
        currentGameId,
        walletState.address,
        column,
      );

      console.log("Move result:", result);

      // Remove the dropping coin after animation
      setTimeout(() => {
        setDroppingCoins((prev) =>
          prev.filter((coin) => coin.id !== droppingCoin.id),
        );
      }, DROP_DURATION);

      // Reset pending state after a short delay
      setTimeout(() => {
        setIsMovePending(false);
      }, 1000);

      // Game state will be updated automatically through the useEffect
      // that watches convexGameState
    } catch (error) {
      console.error("Failed to make move:", error);
      setIsMovePending(false);
      // Remove the dropping coin on error
      setDroppingCoins((prev) =>
        prev.filter((coin) => coin.id !== `${Date.now()}-${column}`),
      );
    }
  };

  // Show result modal when game ends
  useEffect(() => {
    if (gameData.winner || gameData.isDraw) {
      setTimeout(() => setShowResultModal(true), 1000);
    }
  }, [gameData.winner, gameData.isDraw]);

  // Reset game to start screen
  const resetGame = () => {
    setGameData(initialGameData);
    setGameState("start");
    setShowResultModal(false);
    setDroppingCoins([]);
    setWinningCells([]);
  };

  // Handle result modal actions
  const handleModalClose = () => {
    setShowResultModal(false);
    resetGame();
  };

  const handleRestart = () => {
    setShowResultModal(false);
    const currentWager = gameData.wagerAmount;
    resetGame();
    setTimeout(() => {
      startGame(currentWager);
    }, 100);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <Header />
      <div className="flex-1 pt-20">
        {gameState === "start" && <GameStartScreen onStartGame={startGame} />}

        {gameState === "searching" && (
          <PlayerSearchScreen
            onPlayerFound={onPlayerFound}
            onCancel={cancelSearch}
            wagerAmount={gameData.wagerAmount}
          />
        )}

        {gameState === "playing" && (
          <div className="max-w-4xl mx-auto px-4 py-8">
            {/* Game Header */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}>
              <h1 className="text-3xl font-bold text-foreground mb-2">
                Connect 4 Game
              </h1>
              <div className="flex items-center justify-center gap-6 text-muted-foreground">
                <div>Pot: {(gameData.wagerAmount * 2).toFixed(3)} SOL</div>
                <div>Move: {gameData.totalMoves + 1}</div>
                <div
                  className={`font-semibold ${
                    gameData.currentPlayer === 1
                      ? "text-red-500"
                      : "text-yellow-500"
                  }`}>
                  {gameData.currentPlayer === 1 ? "Red" : "Yellow"} Player's
                  Turn
                </div>
              </div>
            </motion.div>

            {/* Game Board Container */}
            <motion.div
              className="flex flex-col items-center"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}>
              {/* Column Hover Indicators */}
              <div className="grid grid-cols-7 gap-2 mb-4">
                {Array.from({ length: COLS }, (_, col) => (
                  <motion.div
                    key={col}
                    className="flex flex-col items-center cursor-pointer"
                    onMouseEnter={() => setHoveredColumn(col)}
                    onMouseLeave={() => setHoveredColumn(null)}
                    onClick={() => handleColumnClick(col)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}>
                    <motion.div
                      className={`w-12 h-12 rounded-full mb-2 border-2 border-dashed transition-all duration-200 ${
                        hoveredColumn === col
                          ? gameData.currentPlayer === 1
                            ? "border-red-500 bg-red-500/20"
                            : "border-yellow-500 bg-yellow-500/20"
                          : "border-muted-foreground/30"
                      }`}
                      animate={{
                        scale: hoveredColumn === col ? 1.1 : 1,
                        y: hoveredColumn === col ? -2 : 0,
                      }}
                      transition={{ duration: 0.2 }}>
                      {hoveredColumn === col && (
                        <motion.div
                          className={`w-full h-full rounded-full ${
                            gameData.currentPlayer === 1
                              ? "bg-red-500"
                              : "bg-yellow-500"
                          }`}
                          initial={{ scale: 0 }}
                          animate={{ scale: 0.8 }}
                          transition={{ duration: 0.2 }}
                        />
                      )}
                    </motion.div>
                    <motion.div
                      className="text-xs text-muted-foreground"
                      animate={{ opacity: hoveredColumn === col ? 1 : 0.5 }}>
                      ↓
                    </motion.div>
                  </motion.div>
                ))}
              </div>

              {/* Game Board */}
              <motion.div
                className="relative bg-blue-600 p-4 rounded-2xl shadow-2xl overflow-hidden"
                style={{
                  width: COLS * (CELL_SIZE + 8) + 16,
                  height: ROWS * (CELL_SIZE + 8) + 16,
                }}>
                {/* Dropping Coins Animation - Behind the board */}
                <AnimatePresence>
                  {droppingCoins.map((coin) => (
                    <motion.div
                      key={coin.id}
                      className={`absolute rounded-full shadow-lg z-0 ${
                        coin.player === 1 ? "bg-red-500" : "bg-yellow-500"
                      }`}
                      style={{
                        width: CELL_SIZE - 8,
                        height: CELL_SIZE - 8,
                        left: coin.column * (CELL_SIZE + 8) + 20,
                        top: 20,
                      }}
                      initial={{ y: -100 }}
                      animate={{
                        y: coin.targetRow * (CELL_SIZE + 8) + 20,
                      }}
                      transition={{
                        duration: DROP_DURATION / 1000,
                        type: "spring",
                        stiffness: 100,
                        damping: 15,
                        bounce: 0.3,
                      }}
                      exit={{ scale: 0, opacity: 0 }}
                    />
                  ))}
                </AnimatePresence>

                {/* Board Grid - Above the dropping coins */}
                <div className="relative z-10 grid grid-rows-6 grid-cols-7 gap-2">
                  {gameData.board.map((row, rowIndex) =>
                    row.map((cell, colIndex) => {
                      const isWinning = winningCells.some(
                        (winCell) =>
                          winCell.row === rowIndex && winCell.col === colIndex,
                      );

                      return (
                        <motion.div
                          key={`${rowIndex}-${colIndex}`}
                          className="relative"
                          style={{ width: CELL_SIZE, height: CELL_SIZE }}>
                          {/* Cell Background - Creates the hole effect */}
                          <div className="absolute inset-0 bg-blue-800 rounded-full border-2 border-blue-700" />

                          {/* Placed Coin */}
                          {cell !== 0 && (
                            <motion.div
                              className={`absolute inset-1 rounded-full shadow-lg z-20 ${
                                cell === 1 ? "bg-red-500" : "bg-yellow-500"
                              } ${isWinning ? "ring-4 ring-white" : ""}`}
                              initial={{ scale: 0 }}
                              animate={{
                                scale: 1,
                                boxShadow: isWinning
                                  ? "0 0 20px rgba(255,255,255,0.8)"
                                  : "0 4px 8px rgba(0,0,0,0.3)",
                              }}
                              transition={{
                                duration: 0.3,
                                type: "spring",
                                stiffness: 300,
                                damping: 20,
                              }}
                            />
                          )}
                        </motion.div>
                      );
                    }),
                  )}
                </div>
              </motion.div>

              {/* Game Controls */}
              <motion.div
                className="mt-8 flex gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}>
                <button
                  onClick={resetGame}
                  className="px-6 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/80 transition-colors">
                  New Game
                </button>
              </motion.div>
            </motion.div>
          </div>
        )}

        {/* Game Result Modal */}
        <GameResultModal
          gameData={gameData}
          currentUserPlayer={getCurrentUserPlayer()}
          isVisible={showResultModal}
          onClose={handleModalClose}
          onRestart={handleRestart}
        />
      </div>
      <Footer />
    </div>
  );
}
