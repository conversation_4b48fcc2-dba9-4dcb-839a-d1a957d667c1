"use client";

import { motion } from "framer-motion";
import { ThemeToggle } from "./ThemeToggle";
import { useWallet } from "../hooks/useWallet";
import { useEffect, useState } from "react";
import { RefreshCw, Wallet, CheckCircle, XCircle } from "lucide-react";
import WalletDetailsModal from "./WalletDetailsModal";

export default function Header() {
  const { walletState, getBalance, connectWallet, disconnectWallet } =
    useWallet();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [showWalletDetails, setShowWalletDetails] = useState(false);

  // Auto-refresh balance every 30 seconds
  useEffect(() => {
    if (walletState.connected) {
      const interval = setInterval(() => {
        setIsRefreshing(true);
        getBalance().finally(() => setIsRefreshing(false));
      }, 30000); // 30 seconds

      return () => clearInterval(interval);
    }
  }, [walletState.connected, getBalance]);

  const handleRefreshBalance = async () => {
    setIsRefreshing(true);
    try {
      await getBalance();
    } catch (error) {
      console.error("Failed to refresh balance:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleConnectWallet = async () => {
    setConnectionError(null);
    try {
      await connectWallet();
    } catch (error: unknown) {
      console.error("Header wallet connection failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to connect wallet";
      setConnectionError(errorMessage);
      // Clear error after 5 seconds
      setTimeout(() => setConnectionError(null), 5000);
    }
  };

  return (
    <motion.header
      className="bg-background/95 backdrop-blur-md border-b border-border px-6 py-4 fixed top-0 left-0 right-0 z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}>
      <div className="max-w-4xl mx-auto flex items-center justify-between">
        {/* Logo and Name */}
        <div className="flex items-center space-x-3">
          <img src="/logo.svg" alt="Connect4.fun" className="w-10 h-8 " />
          <h2 className="font-bold text-foreground">CONNECT4.FUN</h2>
        </div>

        {/* Theme Toggle and Wallet Info */}
        <div className="flex items-center space-x-4">
          <ThemeToggle />

          {/* Wallet Status Row */}
          <div className="flex items-center space-x-3 bg-card rounded-lg px-4 py-2 border border-border ">
            {/* Connection Status */}
            <div className="flex items-center space-x-2">
              {walletState.connected ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : (
                <XCircle className="w-4 h-4 text-red-500" />
              )}
              <Wallet className="w-4 h-4 text-muted-foreground" />
            </div>

            {/* Balance or Connect Button */}
            <div className="text-foreground">
              {walletState.connected ? (
                <button
                  onClick={() => setShowWalletDetails(true)}
                  className="text-sm font-semibold hover:text-blue-400 transition-colors">
                  {walletState.balance?.toFixed(4) || "0.0000"} SOL
                </button>
              ) : (
                <div className="flex flex-col items-center">
                  <button
                    onClick={handleConnectWallet}
                    disabled={walletState.connecting}
                    className="text-sm font-semibold text-blue-500 hover:text-blue-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                    {walletState.connecting
                      ? "Connecting..."
                      : "Connect Phantom"}
                  </button>
                  {connectionError && (
                    <div className="text-xs text-red-500 mt-1 max-w-32 text-center">
                      {connectionError}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Refresh Button */}
            <button
              onClick={handleRefreshBalance}
              disabled={!walletState.connected || isRefreshing}
              className="p-1 hover:bg-muted rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
              <RefreshCw
                className={`w-4 h-4 text-muted-foreground ${
                  isRefreshing ? "animate-spin" : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Wallet Details Modal */}
      <WalletDetailsModal
        isVisible={showWalletDetails}
        onClose={() => setShowWalletDetails(false)}
        walletAddress={walletState.address || ""}
        balance={walletState.balance}
        onDisconnect={disconnectWallet}
      />
    </motion.header>
  );
}
