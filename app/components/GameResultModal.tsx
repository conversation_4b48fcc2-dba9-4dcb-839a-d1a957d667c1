"use client";

import { motion, AnimatePresence } from "framer-motion";
import { GameData } from "./Connect4Game";

interface GameResultModalProps {
  gameData: GameData;
  currentUserPlayer: 1 | 2;
  isVisible: boolean;
  onClose: () => void;
  onRestart: () => void;
}

export default function GameResultModal({
  gameData,
  currentUserPlayer,
  isVisible,
  onClose,
  onRestart,
}: GameResultModalProps) {
  const isWinner = gameData.winner === currentUserPlayer;
  const isDraw = gameData.isDraw;
  const totalPot = gameData.wagerAmount * 2;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-md w-full mx-4"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <div className="text-center">
              {/* Result Icon */}
              <motion.div
                className="text-8xl mb-6"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              >
                {isDraw ? "🤝" : isWinner ? "🏆" : "😔"}
              </motion.div>

              {/* Result Title */}
              <motion.h2
                className="text-3xl font-bold text-foreground mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {isDraw
                  ? "It's a Draw!"
                  : isWinner
                  ? "You Won!"
                  : "You Lost!"}
              </motion.h2>

              {/* Result Details */}
              <motion.div
                className="space-y-4 mb-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {!isDraw && (
                  <div className="text-lg text-muted-foreground">
                    {isWinner
                      ? `🎉 Congratulations! You won ${totalPot.toFixed(3)} SOL!`
                      : `Better luck next time! You lost ${gameData.wagerAmount.toFixed(3)} SOL.`}
                  </div>
                )}

                {isDraw && (
                  <div className="text-lg text-muted-foreground">
                    Good game! Your stake has been returned.
                  </div>
                )}

                <div className="bg-muted rounded-lg p-4 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Game Duration:</span>
                    <span>{gameData.totalMoves} moves</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Your Stake:</span>
                    <span>{gameData.wagerAmount.toFixed(3)} SOL</span>
                  </div>
                  <div className="flex justify-between text-sm font-semibold">
                    <span>Total Pot:</span>
                    <span>{totalPot.toFixed(3)} SOL</span>
                  </div>
                  {!isDraw && (
                    <div className="flex justify-between text-sm">
                      <span>Your Result:</span>
                      <span className={isWinner ? "text-green-500" : "text-red-500"}>
                        {isWinner ? `+${totalPot.toFixed(3)} SOL` : `-${gameData.wagerAmount.toFixed(3)} SOL`}
                      </span>
                    </div>
                  )}
                </div>

                {isWinner && (
                  <motion.div
                    className="text-sm text-green-500 bg-green-500/10 rounded-lg p-3 border border-green-500/20"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 }}
                  >
                    💰 Winnings have been sent to your Phantom wallet!
                  </motion.div>
                )}
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                className="flex gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <button
                  onClick={onRestart}
                  className="flex-1 py-3 px-6 btn-gradient rounded-lg font-semibold hover:scale-105 transition-transform"
                >
                  Play Again
                </button>
                <button
                  onClick={onClose}
                  className="flex-1 py-3 px-6 bg-muted text-foreground rounded-lg hover:bg-muted/80 transition-colors"
                >
                  Back to Menu
                </button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
