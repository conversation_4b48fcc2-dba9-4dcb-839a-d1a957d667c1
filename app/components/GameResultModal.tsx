"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { GameData } from "./Connect4Game";
import { useCustomWallet } from "../hooks/useCustomWallet";
import { calculateWinnerPayout } from "../config/game";

interface GameResultModalProps {
  gameData: GameData;
  currentUserPlayer: 1 | 2;
  isVisible: boolean;
  onClose: () => void;
  onRestart?: () => void;
}

export default function GameResultModal({
  gameData,
  currentUserPlayer,
  isVisible,
  onClose,
  onRestart,
}: GameResultModalProps) {
  const { sendGameTransaction, walletState, getBalance } = useCustomWallet();
  const [payoutSent, setPayoutSent] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [payoutAttempted, setPayoutAttempted] = useState(false);

  const winnerPayout = calculateWinnerPayout(gameData.wagerAmount);
  const isWinner = gameData.winner === currentUserPlayer;
  const isDraw = gameData.isDraw;

  // Auto-send payout when modal opens (if current user won)
  useEffect(() => {
    const sendPayout = async () => {
      if (
        isVisible &&
        gameData.winner &&
        isWinner &&
        !payoutSent &&
        !isProcessing &&
        !payoutAttempted &&
        walletState.address
      ) {
        setPayoutAttempted(true);
        try {
          setIsProcessing(true);

          console.log(
            `Auto-payout: Sending ${winnerPayout} SOL to winner (You): ${walletState.address}`,
          );

          // Send payout to current user (winner)
          const signature = await sendGameTransaction(
            "payout",
            winnerPayout,
            walletState.address,
          );

          setPayoutSent(true);
          setIsProcessing(false);
          console.log("Auto-payout completed:", signature);

          // Force balance refresh after payout
          setTimeout(() => {
            console.log("Refreshing balance after payout...");
            getBalance();
          }, 500);
        } catch (error) {
          console.error("Auto-payout error:", error);
          setIsProcessing(false);
        }
      } else if (
        isVisible &&
        isDraw &&
        !payoutSent &&
        !isProcessing &&
        !payoutAttempted &&
        walletState.address
      ) {
        setPayoutAttempted(true);
        // Return stake for draw
        try {
          setIsProcessing(true);

          console.log(
            `Draw: Returning ${gameData.wagerAmount} SOL stake to: ${walletState.address}`,
          );

          const signature = await sendGameTransaction(
            "payout",
            gameData.wagerAmount,
            walletState.address,
          );

          setPayoutSent(true);
          setIsProcessing(false);
          console.log("Stake returned for draw:", signature);

          // Force balance refresh after stake return
          setTimeout(() => {
            console.log("Refreshing balance after stake return...");
            getBalance();
          }, 500);
        } catch (error) {
          console.error("Stake return error:", error);
          setIsProcessing(false);
        }
      }
    };

    // Only run payout if modal just became visible
    if (isVisible) {
      const timer = setTimeout(sendPayout, 1000);
      return () => clearTimeout(timer);
    }
  }, [
    isVisible,
    gameData.winner,
    gameData.wagerAmount,
    isDraw,
    isWinner,
    payoutSent,
    isProcessing,
    payoutAttempted,
    sendGameTransaction,
    winnerPayout,
    walletState.address,
    getBalance,
  ]);

  // Auto-close modal after 5 seconds (except for draws)
  useEffect(() => {
    if (isVisible && !isDraw) {
      const timer = setTimeout(() => {
        onClose();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose, isDraw]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isVisible) {
      setPayoutSent(false);
      setIsProcessing(false);
      setPayoutAttempted(false);
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}>
        <motion.div
          className="bg-card p-8 rounded-2xl border-2 max-w-md w-full mx-4 text-center"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}>
          {isDraw ? (
            // Draw Result
            <>
              <div className="text-6xl mb-4">🤝</div>
              <h2 className="text-2xl font-bold text-foreground mb-2">
                It&apos;s a Draw!
              </h2>
              <p className="text-muted-foreground mb-4">
                No winner this time. Your stake has been returned.
              </p>
              <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mb-4">
                <p className="text-sm text-blue-600">Stake Returned</p>
                <p className="text-lg font-semibold text-blue-700">
                  +{gameData.wagerAmount.toFixed(3)} SOL
                </p>
                {payoutSent && (
                  <p className="text-xs text-blue-600 mt-2">
                    ✅ Stake returned to your wallet!
                  </p>
                )}
              </div>

              {onRestart && (
                <div className="flex space-x-3">
                  <button
                    onClick={onRestart}
                    className="flex-1 bg-green-500 text-white py-2 px-4 rounded-lg font-semibold hover:bg-green-600 transition-colors">
                    🔄 Play Again
                  </button>
                  <button
                    onClick={onClose}
                    className="flex-1 bg-gray-500 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                    🏠 Main Menu
                  </button>
                </div>
              )}
            </>
          ) : isWinner ? (
            // Winner Result
            <>
              <div className="text-6xl mb-4">🎉</div>
              <h2 className="text-2xl font-bold text-green-500 mb-2">
                You Won!
              </h2>
              <p className="text-muted-foreground mb-4">
                Congratulations! You&apos;ve won the match.
              </p>
              <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                <p className="text-sm text-green-600">Prize Won</p>
                <p className="text-2xl font-bold text-green-700">
                  +{winnerPayout.toFixed(3)} SOL
                </p>
                {isProcessing && (
                  <p className="text-xs text-green-600 mt-2">
                    Sending payout...
                  </p>
                )}
                {payoutSent && (
                  <p className="text-xs text-green-600 mt-2">
                    ✅ Payout sent to your wallet!
                  </p>
                )}
              </div>
            </>
          ) : (
            // Loser Result
            <>
              <div className="text-6xl mb-4">😔</div>
              <h2 className="text-2xl font-bold text-red-500 mb-2">You Lost</h2>
              <p className="text-muted-foreground mb-4">
                Better luck next time! Your opponent played well.
              </p>
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <p className="text-sm text-red-600">Stake Lost</p>
                <p className="text-xl font-semibold text-red-700">
                  -{gameData.wagerAmount.toFixed(3)} SOL
                </p>
              </div>
            </>
          )}

          {!isDraw && (
            <div className="mt-6 text-xs text-muted-foreground">
              This modal will close automatically in a few seconds
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
