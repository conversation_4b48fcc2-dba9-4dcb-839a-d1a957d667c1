"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { GameData, CellState } from "./Connect4Game";

interface GameplayScreenProps {
  gameData: GameData;
  onMakeMove: (column: number) => void;
  onTimeExpired: () => void;
  currentUserPlayer: 1 | 2; // Which player is the current user
}

export default function GameplayScreen({
  gameData,
  onMakeMove,
  onTimeExpired,
  currentUserPlayer,
}: GameplayScreenProps) {
  const [timeLeft, setTimeLeft] = useState(10);
  const [hoveredColumn, setHoveredColumn] = useState<number | null>(null);
  const [lastPlacedPiece, setLastPlacedPiece] = useState<{
    row: number;
    col: number;
  } | null>(null);

  // Calculate time left based on server data
  const calculateTimeLeft = () => {
    if (!gameData.turnStartTime || !gameData.turnTimeLimit) {
      console.log("No server timer data, using default 10s");
      return 10;
    }

    const now = Date.now();
    const elapsed = now - gameData.turnStartTime;
    const remaining = Math.max(
      0,
      Math.ceil((gameData.turnTimeLimit - elapsed) / 1000),
    );
    console.log(`Timer calc: elapsed=${elapsed}ms, remaining=${remaining}s`);
    return remaining;
  };

  // Update timer based on server data and current player
  useEffect(() => {
    console.log(`Timer effect triggered:`, {
      currentPlayer: gameData.currentPlayer,
      currentUserPlayer,
      turnStartTime: gameData.turnStartTime,
      turnTimeLimit: gameData.turnTimeLimit,
    });

    if (gameData.currentPlayer === currentUserPlayer) {
      if (gameData.turnStartTime && gameData.turnTimeLimit) {
        // Use server timer data
        const serverTimeLeft = calculateTimeLeft();
        setTimeLeft(serverTimeLeft);
        console.log(`Timer synced with server: ${serverTimeLeft}s remaining`);
      } else {
        // Fallback to local timer
        setTimeLeft(10);
        console.log("Using fallback timer: 10s");
      }
    } else {
      console.log("Not your turn - timer paused");
    }
  }, [gameData.currentPlayer, gameData.turnStartTime, currentUserPlayer]);

  // Client-side timer countdown (for display only)
  useEffect(() => {
    if (gameData.currentPlayer === currentUserPlayer && timeLeft > 0) {
      console.log(`Starting timer countdown from ${timeLeft}s`);
      const timer = setTimeout(() => {
        if (gameData.turnStartTime && gameData.turnTimeLimit) {
          // Use server-calculated time
          const newTimeLeft = calculateTimeLeft();
          console.log(`Server timer tick: ${timeLeft} -> ${newTimeLeft}`);
          setTimeLeft(newTimeLeft);

          if (newTimeLeft <= 0) {
            console.log("Server timer expired - triggering auto-drop");
            onTimeExpired();
          }
        } else {
          // Use simple countdown
          const newTimeLeft = timeLeft - 1;
          console.log(`Simple timer tick: ${timeLeft} -> ${newTimeLeft}`);
          setTimeLeft(newTimeLeft);

          if (newTimeLeft <= 0) {
            console.log("Simple timer expired - triggering auto-drop");
            onTimeExpired();
          }
        }
      }, 1000);
      return () => clearTimeout(timer);
    } else if (gameData.currentPlayer === currentUserPlayer && timeLeft === 0) {
      console.log("Timer at 0 - triggering auto-drop");
      onTimeExpired();
    }
  }, [timeLeft, gameData.currentPlayer, currentUserPlayer, onTimeExpired]);

  const handleColumnClick = (column: number) => {
    // Only allow moves if it's the current user's turn
    if (gameData.currentPlayer !== currentUserPlayer) {
      console.log("Not your turn!");
      return;
    }

    // Check if column is full
    if (gameData.board[0][column] !== 0) return;

    // Find the row where the piece will land
    let targetRow = -1;
    for (let row = 5; row >= 0; row--) {
      if (gameData.board[row][column] === 0) {
        targetRow = row;
        break;
      }
    }

    // No transaction needed - just make the move
    setLastPlacedPiece({ row: targetRow, col: column });
    onMakeMove(column);

    // Clear the animation after a delay
    setTimeout(() => setLastPlacedPiece(null), 1000);
  };

  const getCellColor = (cell: CellState) => {
    if (cell === 1) return "bg-red-500";
    if (cell === 2) return "bg-yellow-500";
    return "bg-background";
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-2xl mx-auto px-6 py-8">
        {/* Current Player Card */}
        <motion.div
          className={`bg-card rounded-xl p-6 border-2 transition-all duration-300 mb-8 ${
            gameData.currentPlayer === 1
              ? "border-red-500 bg-red-500/10"
              : "border-yellow-500 bg-yellow-500/10"
          }`}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}>
          <div className="flex items-center justify-between">
            {/* Current Player Info */}
            <div className="flex items-center space-x-4">
              <div
                className={`w-16 h-16 rounded-full flex items-center justify-center shadow-lg ${
                  gameData.currentPlayer === 1
                    ? "bg-gradient-to-br from-red-500 to-red-600"
                    : "bg-gradient-to-br from-yellow-500 to-yellow-600"
                }`}>
                <div className="text-white font-bold text-xl">
                  {gameData.currentPlayer === 1 ? "🔴" : "🟡"}
                </div>
              </div>
              <div>
                <div className="text-foreground font-bold text-xl">
                  {gameData.currentPlayer === currentUserPlayer
                    ? "Your Turn"
                    : "Opponent&apos;s Turn"}
                </div>
                <div className="text-muted-foreground text-sm">
                  {gameData.currentPlayer === currentUserPlayer
                    ? "You"
                    : "Opponent"}{" "}
                  • {gameData.currentPlayer === 1 ? "Red" : "Yellow"} Coins
                </div>
              </div>
            </div>
            <div
              className={`text-2xl font-bold mt-2 ${
                gameData.currentPlayer === 1
                  ? "text-red-500"
                  : "text-yellow-500"
              }`}>
              {gameData.currentPlayer === currentUserPlayer
                ? `${timeLeft}s`
                : "⏸️"}
            </div>

            {/* Game Info */}
            <div className="text-right">
              <div className="text-foreground font-semibold text-lg">
                Total Pot: {(gameData.wagerAmount * 2).toFixed(3)} SOL
              </div>
              <div className="text-muted-foreground text-sm">
                Move {gameData.totalMoves + 1} • {gameData.wagerAmount} SOL each
              </div>
            </div>
          </div>
        </motion.div>

        {/* Game Board */}
        <motion.div
          className="flex flex-col items-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}>
          {/* Drop Indicators - Only show for current user's turn */}
          {gameData.currentPlayer === currentUserPlayer && (
            <div className="grid grid-cols-7 gap-4 mb-4 px-4">
              {Array.from({ length: 7 }, (_, col) => (
                <motion.div
                  key={col}
                  className="flex flex-col items-center cursor-pointer"
                  onMouseEnter={() => setHoveredColumn(col)}
                  onMouseLeave={() => setHoveredColumn(null)}
                  onClick={() => handleColumnClick(col)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{
                    scale: 0.9,
                    transition: { duration: 0.1 },
                  }}>
                  <motion.img
                    src={
                      gameData.currentPlayer === 1
                        ? "/red-coin.png"
                        : "/yellow-coin.png"
                    }
                    alt={`Player ${gameData.currentPlayer} coin`}
                    className="w-12 h-12 mb-2 drop-shadow-lg"
                    animate={{
                      scale: hoveredColumn === col ? 1.15 : 1,
                      y: hoveredColumn === col ? -2 : 0,
                    }}
                    transition={{
                      duration: 0.2,
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                    }}
                  />
                  <motion.img
                    src="/arrow-down.png"
                    alt="Drop arrow"
                    className="w-6 h-6"
                    animate={{
                      y: hoveredColumn === col ? 2 : 0,
                      scale: hoveredColumn === col ? 1.1 : 1,
                    }}
                    transition={{ duration: 0.2 }}
                  />
                </motion.div>
              ))}
            </div>
          )}

          {/* Opponent's turn indicator */}
          {gameData.currentPlayer !== currentUserPlayer && (
            <div className=" max-w-2xl mx-auto  mb-4 px-4 py-2 bg-muted/50 rounded-lg">
              <div className="text-center text-muted-foreground">
                Waiting for opponent&apos;s move...
              </div>
            </div>
          )}

          {/* Game Grid - Original Board Design */}
          <motion.div
            className="bg-accent p-4 rounded-2xl border-2 border-accent"
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}>
            <div className="grid grid-rows-6 gap-2">
              {gameData.board.map((row, rowIndex) => (
                <div key={rowIndex} className="grid grid-cols-7 gap-2">
                  {row.map((cell, colIndex) => {
                    const isLastPlaced =
                      lastPlacedPiece &&
                      lastPlacedPiece.row === rowIndex &&
                      lastPlacedPiece.col === colIndex;

                    return (
                      <motion.div
                        key={`${rowIndex}-${colIndex}`}
                        className="w-14 h-14 rounded-full border-2 border-background-secondary flex items-center justify-center relative"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{
                          duration: 0.3,
                          delay: 0.6 + rowIndex * 0.05 + colIndex * 0.02,
                        }}>
                        <motion.div
                          className={`w-10 h-10 rounded-full ${getCellColor(
                            cell,
                          )} shadow-lg relative z-10`}
                          animate={cell !== 0 ? { scale: [0, 1.2, 1] } : {}}
                          transition={{ duration: 0.4, ease: "easeOut" }}
                        />
                        {/* Pop animation for newly placed pieces */}
                        {isLastPlaced && (
                          <motion.div
                            className="absolute inset-0 rounded-full border-4 border-white"
                            initial={{ scale: 1, opacity: 1 }}
                            animate={{
                              scale: [1, 1.5, 2],
                              opacity: [1, 0.7, 0],
                            }}
                            transition={{
                              duration: 0.6,
                              ease: "easeOut",
                            }}
                          />
                        )}
                        {isLastPlaced && (
                          <motion.div
                            className={`absolute inset-0 rounded-full ${getCellColor(
                              cell,
                            )}`}
                            initial={{ scale: 1.2 }}
                            animate={{ scale: [1.2, 1.4, 1] }}
                            transition={{
                              duration: 0.5,
                              ease: "easeOut",
                            }}
                          />
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}
