"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";

export default function WalletDetection() {
  const [phantomDetected, setPhantomDetected] = useState(false);
  const [phantomInstalled, setPhantomInstalled] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkPhantom = () => {
      if (typeof window !== 'undefined') {
        const isInstalled = !!(window as any).phantom?.solana;
        const isDetected = !!(window as any).phantom?.solana?.isPhantom;
        
        setPhantomInstalled(isInstalled);
        setPhantomDetected(isDetected);
        setLoading(false);
        
        console.log('Phantom detection:', {
          installed: isInstalled,
          detected: isDetected,
          phantom: (window as any).phantom
        });
      }
    };

    // Check immediately
    checkPhantom();

    // Also check after a delay in case Phantom loads later
    const timer = setTimeout(check<PERSON>hantom, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <motion.div
        className="fixed top-4 left-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}>
        <div className="text-sm text-muted-foreground">
          🔍 Detecting Phantom wallet...
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="fixed top-4 left-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}>
      <div className="text-sm">
        <div className="text-foreground font-semibold mb-2">🔍 Wallet Detection</div>
        
        <div className="space-y-1 text-xs">
          <div className="flex items-center space-x-2">
            <span className={phantomInstalled ? "text-green-400" : "text-red-400"}>
              {phantomInstalled ? "✅" : "❌"}
            </span>
            <span className="text-muted-foreground">Phantom Installed</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={phantomDetected ? "text-green-400" : "text-red-400"}>
              {phantomDetected ? "✅" : "❌"}
            </span>
            <span className="text-muted-foreground">Phantom Detected</span>
          </div>
        </div>
        
        {!phantomInstalled && (
          <div className="mt-3 p-2 bg-yellow-500/10 rounded border border-yellow-500/20">
            <div className="text-yellow-400 text-xs font-semibold">Action Required</div>
            <div className="text-yellow-300 text-xs">
              Install Phantom wallet extension and refresh the page
            </div>
            <a 
              href="https://phantom.app" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-400 text-xs underline hover:text-blue-300">
              Download Phantom →
            </a>
          </div>
        )}
        
        {phantomInstalled && !phantomDetected && (
          <div className="mt-3 p-2 bg-orange-500/10 rounded border border-orange-500/20">
            <div className="text-orange-400 text-xs font-semibold">Setup Required</div>
            <div className="text-orange-300 text-xs">
              Unlock your Phantom wallet and refresh the page
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}
