"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useCustomWallet } from "../hooks/useCustomWallet";
import { Copy, Download, Upload, Wallet, X } from "lucide-react";

interface CustomWalletProviderProps {
  children: React.ReactNode;
}

export default function CustomWalletProvider({
  children,
}: CustomWalletProviderProps) {
  const {
    walletState,
    createWallet,
    connectExistingWallet,
    connectWithSeedPhrase,
    getBalance,
    requestAirdrop,
    disconnect,
    exportPrivateKey,
    exportSeedPhrase,
  } = useCustomWallet();

  const [showWalletModal, setShowWalletModal] = useState(false);
  const [importKey, setImportKey] = useState("");
  const [importSeed, setImportSeed] = useState("");
  const [showImport, setShowImport] = useState(false);
  const [showImportSeed, setShowImportSeed] = useState(false);
  const [showExport, setShowExport] = useState(false);

  const handleCreateWallet = async () => {
    try {
      await createWallet();
      setShowWalletModal(false);
    } catch (error) {
      console.error("Failed to create wallet:", error);
    }
  };

  const handleImportWallet = async () => {
    try {
      await connectExistingWallet(importKey);
      setImportKey("");
      setShowImport(false);
      setShowWalletModal(false);
    } catch (error) {
      console.error("Failed to import wallet:", error);
      alert("Invalid private key format");
    }
  };

  const handleImportSeedPhrase = async () => {
    try {
      await connectWithSeedPhrase(importSeed);
      setImportSeed("");
      setShowImportSeed(false);
      setShowWalletModal(false);
    } catch (error) {
      console.error("Failed to import seed phrase:", error);
      alert("Invalid seed phrase format");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert("Copied to clipboard!");
  };

  const copyAddress = () => {
    if (walletState.address) {
      copyToClipboard(walletState.address);
    }
  };

  const copyPrivateKey = () => {
    const privateKey = exportPrivateKey();
    if (privateKey) {
      copyToClipboard(privateKey);
    }
  };

  const copySeedPhrase = () => {
    const seedPhrase = exportSeedPhrase();
    if (seedPhrase) {
      copyToClipboard(seedPhrase);
    }
  };

  return (
    <>
      {children}

      {/* Wallet Status Bar */}
      <div className="fixed top-4 right-4 z-50">
        {walletState.connected ? (
          <motion.div
            className="bg-card border border-border rounded-lg p-3 shadow-lg"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <Wallet className="w-4 h-4 text-white" />
              </div>
              <div>
                <div className="text-sm font-semibold text-foreground">
                  {walletState.balance.toFixed(4)} SOL
                </div>
                <div className="text-xs text-muted-foreground">
                  {walletState.address?.slice(0, 8)}...
                  {walletState.address?.slice(-4)}
                </div>
              </div>
              <button
                onClick={() => setShowWalletModal(true)}
                className="text-muted-foreground hover:text-foreground">
                ⚙️
              </button>
            </div>
          </motion.div>
        ) : (
          <button
            onClick={() => setShowWalletModal(true)}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors">
            Connect Wallet
          </button>
        )}
      </div>

      {/* Wallet Modal */}
      <AnimatePresence>
        {showWalletModal && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}>
            <motion.div
              className="bg-card border border-border rounded-xl p-6 max-w-md w-full mx-4"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}>
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-foreground">
                  {walletState.connected ? "Wallet Settings" : "Connect Wallet"}
                </h2>
                <button
                  onClick={() => setShowWalletModal(false)}
                  className="text-muted-foreground hover:text-foreground">
                  <X className="w-5 h-5" />
                </button>
              </div>

              {!walletState.connected ? (
                <div className="space-y-4">
                  <button
                    onClick={handleCreateWallet}
                    className="w-full bg-green-500 text-white py-3 rounded-lg font-semibold hover:bg-green-600 transition-colors flex items-center justify-center space-x-2">
                    <Wallet className="w-5 h-5" />
                    <span>Create New Wallet</span>
                  </button>

                  <button
                    onClick={() => setShowImportSeed(true)}
                    className="w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2">
                    <Upload className="w-5 h-5" />
                    <span>Import with Seed Phrase</span>
                  </button>

                  <button
                    onClick={() => setShowImport(true)}
                    className="w-full bg-gray-500 text-white py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors flex items-center justify-center space-x-2">
                    <Upload className="w-5 h-5" />
                    <span>Import with Private Key</span>
                  </button>

                  {showImportSeed && (
                    <div className="space-y-3">
                      <textarea
                        value={importSeed}
                        onChange={(e) => setImportSeed(e.target.value)}
                        placeholder="Enter your 12-word seed phrase here..."
                        className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                        rows={3}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={handleImportSeedPhrase}
                          className="flex-1 bg-green-500 text-white py-2 rounded-lg">
                          Import
                        </button>
                        <button
                          onClick={() => setShowImportSeed(false)}
                          className="flex-1 bg-gray-500 text-white py-2 rounded-lg">
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}

                  {showImport && (
                    <div className="space-y-3">
                      <textarea
                        value={importKey}
                        onChange={(e) => setImportKey(e.target.value)}
                        placeholder="Paste private key here..."
                        className="w-full p-3 border border-border rounded-lg bg-background text-foreground"
                        rows={3}
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={handleImportWallet}
                          className="flex-1 bg-green-500 text-white py-2 rounded-lg">
                          Import
                        </button>
                        <button
                          onClick={() => setShowImport(false)}
                          className="flex-1 bg-gray-500 text-white py-2 rounded-lg">
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-muted/50 rounded-lg p-4">
                    <div className="text-sm text-muted-foreground mb-1">
                      Balance
                    </div>
                    <div className="text-2xl font-bold text-foreground">
                      {walletState.balance.toFixed(4)} SOL
                    </div>
                  </div>

                  <div className="bg-muted/50 rounded-lg p-4">
                    <div className="text-sm text-muted-foreground mb-1">
                      Address
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-sm font-mono text-foreground flex-1">
                        {walletState.address?.slice(0, 20)}...
                        {walletState.address?.slice(-10)}
                      </div>
                      <button
                        onClick={copyAddress}
                        className="text-blue-500 hover:text-blue-600">
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <button
                      onClick={() => setShowExport(!showExport)}
                      className="bg-orange-500 text-white py-2 rounded-lg flex items-center justify-center space-x-2">
                      <Download className="w-4 h-4" />
                      <span>Export Key</span>
                    </button>
                    <button
                      onClick={getBalance}
                      className="bg-blue-500 text-white py-2 rounded-lg">
                      Refresh
                    </button>
                  </div>

                  <button
                    onClick={requestAirdrop}
                    className="w-full bg-green-500 text-white py-3 rounded-lg font-semibold mb-4">
                    💰 Add 2 SOL (Faucet)
                  </button>

                  {showExport && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 space-y-4">
                      {walletState.seedPhrase && (
                        <div>
                          <div className="text-red-600 text-sm font-semibold mb-2">
                            🌱 Seed Phrase (12 words)
                          </div>
                          <div className="text-xs font-mono bg-white p-2 rounded border break-all">
                            {exportSeedPhrase()}
                          </div>
                          <button
                            onClick={copySeedPhrase}
                            className="mt-2 text-red-600 text-sm hover:underline">
                            Copy Seed Phrase
                          </button>
                        </div>
                      )}

                      <div>
                        <div className="text-red-600 text-sm font-semibold mb-2">
                          🔑 Private Key (Advanced)
                        </div>
                        <div className="text-xs font-mono bg-white p-2 rounded border break-all">
                          {exportPrivateKey()}
                        </div>
                        <button
                          onClick={copyPrivateKey}
                          className="mt-2 text-red-600 text-sm hover:underline">
                          Copy Private Key
                        </button>
                      </div>
                    </div>
                  )}

                  <button
                    onClick={disconnect}
                    className="w-full bg-red-500 text-white py-2 rounded-lg">
                    Disconnect
                  </button>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
