import { X, Copy, ExternalLink, LogOut } from "lucide-react";
import { useState } from "react";

interface WalletDetailsModalProps {
  isVisible: boolean;
  onClose: () => void;
  walletAddress: string;
  balance: number | null;
  onDisconnect: () => void;
}

export default function WalletDetailsModal({
  isVisible,
  onClose,
  walletAddress,
  balance,
  onDisconnect,
}: WalletDetailsModalProps) {
  const [copied, setCopied] = useState(false);

  if (!isVisible) return null;

  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(walletAddress);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy address:", error);
    }
  };

  const openInExplorer = () => {
    window.open(`https://explorer.solana.com/address/${walletAddress}`, '_blank');
  };

  const handleDisconnect = () => {
    onDisconnect();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg p-6 max-w-md w-full border border-border relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 hover:bg-muted rounded transition-colors"
        >
          <X className="w-4 h-4 text-muted-foreground" />
        </button>

        <div className="text-center">
          {/* Phantom Logo */}
          <div className="w-16 h-16 bg-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span className="text-2xl">👻</span>
          </div>
          
          <h2 className="text-xl font-bold text-foreground mb-2">
            Phantom Wallet
          </h2>
          
          {/* Balance */}
          <div className="bg-muted/50 rounded-lg p-4 mb-4">
            <div className="text-sm text-muted-foreground mb-1">Balance</div>
            <div className="text-2xl font-bold text-foreground">
              {balance?.toFixed(4) || "0.0000"} SOL
            </div>
          </div>

          {/* Wallet Address */}
          <div className="bg-muted/50 rounded-lg p-4 mb-6">
            <div className="text-sm text-muted-foreground mb-2">Wallet Address</div>
            <div className="text-sm font-mono text-foreground break-all mb-3">
              {walletAddress}
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={copyAddress}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                <Copy className="w-4 h-4" />
                <span>{copied ? "Copied!" : "Copy"}</span>
              </button>
              
              <button
                onClick={openInExplorer}
                className="flex-1 flex items-center justify-center space-x-2 bg-gray-600 text-white py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors text-sm"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Explorer</span>
              </button>
            </div>
          </div>

          {/* Disconnect Button */}
          <button
            onClick={handleDisconnect}
            className="w-full flex items-center justify-center space-x-2 bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-semibold"
          >
            <LogOut className="w-4 h-4" />
            <span>Disconnect Wallet</span>
          </button>
        </div>
      </div>
    </div>
  );
}
