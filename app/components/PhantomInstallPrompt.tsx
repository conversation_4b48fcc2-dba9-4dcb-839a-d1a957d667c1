import { ExternalLink, Download } from "lucide-react";

interface PhantomInstallPromptProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function PhantomInstallPrompt({
  isVisible,
  onClose,
}: PhantomInstallPromptProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg p-6 max-w-md w-full border border-border">
        <div className="text-center">
          <div className="text-4xl mb-4">👻</div>
          <h2 className="text-xl font-bold text-foreground mb-2">
            Phantom Wallet Required
          </h2>
          <p className="text-muted-foreground mb-6">
            To play Connect4.fun, you need to install the Phantom wallet
            extension.
          </p>

          <div className="space-y-4">
            <a
              href="https://phantom.app/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center space-x-2 bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors">
              <Download className="w-4 h-4" />
              <span>Install Phantom Wallet</span>
              <ExternalLink className="w-4 h-4" />
            </a>

            <div className="text-sm text-muted-foreground">
              <p className="mb-2">After installing:</p>
              <ol className="text-left space-y-1">
                <li>1. Refresh this page</li>
                <li>2. Click &quot;Connect Phantom&quot;</li>
                <li>3. Approve the connection</li>
                <li>4. Start playing!</li>
              </ol>
            </div>
          </div>

          <button
            onClick={onClose}
            className="mt-4 text-sm text-muted-foreground hover:text-foreground transition-colors">
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
