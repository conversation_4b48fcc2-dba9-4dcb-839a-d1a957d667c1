"use client";

import { useWallet } from "../hooks/useWallet";
import { motion } from "framer-motion";

export default function WalletDebugInfo() {
  const { walletState, getBalance } = useWallet();

  if (!walletState.connected) return null;

  return (
    <motion.div
      className="fixed bottom-4 right-4 bg-card border border-border rounded-lg p-4 shadow-lg max-w-sm z-50"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}>
      <div className="text-sm">
        <div className="text-foreground font-semibold mb-2">
          🔍 Wallet Debug Info
        </div>

        <div className="space-y-1 text-xs">
          <div className="text-muted-foreground">
            <span className="font-medium">Status:</span>
            <span className="text-green-400 ml-1">Connected</span>
          </div>

          <div className="text-muted-foreground">
            <span className="font-medium">Address:</span>
            <div className="font-mono text-foreground break-all">
              {walletState.address}
            </div>
          </div>

          <div className="text-muted-foreground">
            <span className="font-medium">Balance:</span>
            <span className="text-foreground ml-1">
              {walletState.balance?.toFixed(4) || "Loading..."} SOL
            </span>
          </div>

          <div className="text-muted-foreground">
            <span className="font-medium">Network:</span>
            <span className="text-yellow-400 ml-1">Devnet</span>
          </div>
        </div>

        <button
          onClick={getBalance}
          className="mt-3 w-full py-1 px-2 bg-primary text-primary-foreground rounded text-xs hover:bg-primary/90 transition-colors">
          Refresh Balance
        </button>
      </div>
    </motion.div>
  );
}
