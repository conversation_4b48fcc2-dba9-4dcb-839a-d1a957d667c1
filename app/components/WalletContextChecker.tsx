"use client";

import { useWallet } from '@solana/wallet-adapter-react';
import { useConnection } from '@solana/wallet-adapter-react';
import { motion } from "framer-motion";

export default function WalletContextChecker() {
  const walletContext = useWallet();
  const connectionContext = useConnection();

  const {
    wallets,
    wallet,
    publicKey,
    connected,
    connecting,
    disconnecting,
    connect,
    disconnect,
    sendTransaction
  } = walletContext;

  const { connection } = connectionContext;

  return (
    <motion.div
      className="fixed top-4 right-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50 max-w-sm"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}>
      <div className="text-sm">
        <div className="text-foreground font-semibold mb-3">🔌 Context Checker</div>
        
        <div className="space-y-2 text-xs mb-4">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Wallet Context:</span>
            <span className={walletContext ? "text-green-400" : "text-red-400"}>
              {walletContext ? "✅ Available" : "❌ Missing"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connection Context:</span>
            <span className={connectionContext ? "text-green-400" : "text-red-400"}>
              {connectionContext ? "✅ Available" : "❌ Missing"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Wallets Array:</span>
            <span className="text-foreground">{wallets?.length || 0}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connect Function:</span>
            <span className={connect ? "text-green-400" : "text-red-400"}>
              {connect ? "✅ Available" : "❌ Missing"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Current Wallet:</span>
            <span className="text-foreground">{wallet?.adapter?.name || 'None'}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connection:</span>
            <span className={connection ? "text-green-400" : "text-red-400"}>
              {connection ? "✅ Connected" : "❌ No Connection"}
            </span>
          </div>
        </div>
        
        {!walletContext && (
          <div className="p-2 bg-red-500/10 rounded border border-red-500/20">
            <div className="text-red-400 text-xs font-semibold">Context Error</div>
            <div className="text-red-300 text-xs">
              Wallet context not available. Check WalletProvider setup.
            </div>
          </div>
        )}
        
        {walletContext && !connect && (
          <div className="p-2 bg-orange-500/10 rounded border border-orange-500/20">
            <div className="text-orange-400 text-xs font-semibold">Function Missing</div>
            <div className="text-orange-300 text-xs">
              Connect function not available in context.
            </div>
          </div>
        )}
        
        {walletContext && connect && wallets?.length === 0 && (
          <div className="p-2 bg-yellow-500/10 rounded border border-yellow-500/20">
            <div className="text-yellow-400 text-xs font-semibold">No Wallets</div>
            <div className="text-yellow-300 text-xs">
              No wallet adapters configured.
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
}
