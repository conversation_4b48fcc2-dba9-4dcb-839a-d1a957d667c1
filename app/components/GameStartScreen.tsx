"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";

import { useWallet } from "../hooks/useWallet";
import { calculateTotalStake } from "../config/game";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import PhantomInstallPrompt from "./PhantomInstallPrompt";

interface GameStartScreenProps {
  onStartGame: (wagerAmount: number) => void;
}

const wagerOptions = [
  0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0,
];

export default function GameStartScreen({ onStartGame }: GameStartScreenProps) {
  const [selectedWager, setSelectedWager] = useState(0.001);
  const { walletState, getBalance, sendGameTransaction, connectWallet } =
    useWallet();
  const [showWalletPopup, setShowWalletPopup] = useState(false);
  const [walletError, setWalletError] = useState<string | null>(null);
  const [showPhantomInstall, setShowPhantomInstall] = useState(false);

  const convexMatchmaking = useConvexMatchmaking();
  const waitingCounts = convexMatchmaking.useWaitingCounts() || {};

  // Update balance when wallet connects
  useEffect(() => {
    if (walletState.connected) {
      getBalance();
    }
  }, [walletState.connected, getBalance]);

  const handleConnectWallet = async () => {
    try {
      setWalletError(null);
      await connectWallet();

      // After successful connection, check balance
      if (walletState.connected) {
        const balance = await getBalance();
        console.log("Wallet balance:", balance);

        if (balance !== null && balance < calculateTotalStake(selectedWager)) {
          setWalletError(
            `Insufficient balance. Need ${calculateTotalStake(selectedWager).toFixed(3)} SOL`,
          );
          return;
        }

        setWalletError(null);
      }
    } catch (error: unknown) {
      console.error("Wallet connection failed:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to connect wallet";
      if (
        errorMessage.includes("not detected") ||
        errorMessage.includes("not found")
      ) {
        setShowPhantomInstall(true);
      } else {
        setWalletError(errorMessage);
      }
    }
  };

  const handleStartGame = async () => {
    if (!walletState.connected) return;

    setShowWalletPopup(true);
    setWalletError(null);

    try {
      console.log(`Staking ${selectedWager} SOL to start game...`);

      // Send the stake transaction
      const totalStake = calculateTotalStake(selectedWager);
      const signature = await sendGameTransaction(totalStake);

      console.log("Stake successful, signature:", signature);
      onStartGame(selectedWager);
    } catch (error: unknown) {
      console.error("Staking failed:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Staking failed. Please try again.";
      setWalletError(errorMessage);
    } finally {
      setShowWalletPopup(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          {/* Wager Selection */}
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-4xl mx-auto mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}>
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Left side - Wager options and selection */}
              <div>
                <h4 className="text-2xl font-bold text-foreground mb-6 flex items-center justify-center">
                  Connect & Conqure
                </h4>
                <p className="text-center text-foreground mb-6 flex items-center justify-center">
                  Drop tokens into the grid, line up four to score, and stake
                  your way to victory—on-chain and in real time.
                </p>
                <div className="grid grid-cols-3 md:grid-cols-4 gap-3 mb-6">
                  {wagerOptions.map((amount, index) => (
                    <motion.button
                      key={amount}
                      onClick={() => setSelectedWager(amount)}
                      className={`p-4 rounded-lg font-semibold transition-all duration-200 border ${
                        selectedWager === amount
                          ? "btn-gradient border-transparent"
                          : "bg-background text-foreground border-border hover:bg-muted hover:border-primary"
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.4 + index * 0.05 }}>
                      <div className="text-lg">{amount}</div>
                      <div className="text-xs opacity-70">SOL</div>
                      {waitingCounts[amount] > 0 && (
                        <div className="text-xs text-blue-400 mt-1">
                          👥 {waitingCounts[amount]}
                        </div>
                      )}
                    </motion.button>
                  ))}
                </div>

                <motion.div
                  className="text-center p-4 bg-muted rounded-lg border border-border mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8, duration: 0.6 }}>
                  <span className="text-muted-foreground">
                    Selected Wager:{" "}
                  </span>
                  <span className="text-foreground font-bold text-xl">
                    {selectedWager} SOL
                  </span>
                </motion.div>

                {/* Error Display */}
                {walletError && (
                  <motion.div
                    className="text-center p-4 bg-red-500/10 rounded-lg border border-red-500/20 mb-6"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}>
                    <div className="text-red-400 font-semibold mb-2">
                      ❌ Connection Error
                    </div>
                    <div className="text-muted-foreground text-sm">
                      {walletError}
                    </div>
                  </motion.div>
                )}
                {/* Action Buttons */}
                <motion.div
                  className="flex flex-col gap-4"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}>
                  <motion.button
                    onClick={
                      walletState.connected
                        ? handleStartGame
                        : handleConnectWallet
                    }
                    className="w-full py-4 px-8 rounded-lg font-semibold text-lg btn-gradient"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    {walletState.connected
                      ? "Start Game"
                      : "Connect Wallet & Start Game"}
                  </motion.button>
                </motion.div>
              </div>

              {/* Right side - Game image */}
              <motion.div
                className="flex justify-center lg:justify-end"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}>
                <div className="relative">
                  <img
                    src="/game.png"
                    alt="Connect 4 Game Preview"
                    className="w-full max-w-sm h-auto object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent rounded-lg"></div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* How it Works Section */}
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-4xl mx-auto mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}>
            <h3 className="text-2xl font-bold text-foreground mb-6 flex items-center justify-center">
              {/* <span className="w-2 h-2 bg-primary rounded-full mr-3"></span> */}
              How It Works
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              <motion.div
                className="text-center p-6 bg-muted rounded-lg border border-border"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.2 }}>
                {/* <div className="w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🎯</span>
                </div> */}
                <h4 className="font-semibold text-foreground mb-2">
                  1. Choose Your Stake
                </h4>
                <p className="text-sm text-muted-foreground">
                  Select your wager amount from the available options. Higher
                  stakes mean bigger rewards!
                </p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-muted rounded-lg border border-border"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.4 }}>
                {/* <div className="w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">⚡</span>
                </div> */}
                <h4 className="font-semibold text-foreground mb-2">
                  2. Find Opponent
                </h4>
                <p className="text-sm text-muted-foreground">
                  Get matched with another player who has the same stake amount.
                  Fair and competitive!
                </p>
              </motion.div>

              <motion.div
                className="text-center p-6 bg-muted rounded-lg border border-border"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 1.6 }}>
                {/* <div className="w-16 h-16 bg-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-2xl">🏆</span>
                </div> */}
                <h4 className="font-semibold text-foreground mb-2">
                  3. Play & Win
                </h4>
                <p className="text-sm text-muted-foreground">
                  Connect 4 pieces in a row to win the entire pot. All
                  transactions secured on Solana!
                </p>
              </motion.div>
            </div>
          </motion.div>

          {/* Game Rules Section with Cover Photo  */}
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-4xl mx-auto mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}>
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Right side - Game image */}
              <motion.div
                className="flex justify-center lg:justify-end"
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}>
                <div className="relative">
                  <img
                    src="/cover.png"
                    alt="Connect 4 Game Preview"
                    className="w-full max-w-sm h-auto object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent rounded-lg"></div>
                </div>
              </motion.div>
              {/* Game Rules */}
              <motion.div
                className=""
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}>
                <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center">
                  Game Rules & Mechanics
                </h2>

                <div className="space-y-4 text-foreground">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">
                        Stake to Play
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Each move requires staking SOL tokens. Higher stakes,
                        bigger rewards!
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">
                        Time Pressure
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        10 seconds per move. Think fast or face random
                        placement!
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">
                        Victory Condition
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Connect 4 pieces in a row (horizontal, vertical, or
                        diagonal) to win the entire pot!
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">
                        Blockchain Security
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        All moves are recorded on Solana blockchain for complete
                        transparency and fairness.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                    <div>
                      <h4 className="font-semibold text-foreground mb-1">
                        Instant Payouts
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Winners receive instant SOL payouts directly to their
                        Phantom wallet.
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Support Token Section */}
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.6 }}>
            <h2 className="text-2xl font-bold text-foreground mb-6 flex items-center justify-center">
              <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
              Buy $CONNECT4 Token
            </h2>
            <div className="text-center mb-6">
              <p className="text-muted-foreground mb-4">
                Support the development of CONNECT4.FUN by purchasing our native
                $CONNECT4 token. Your support helps us build the future of
                blockchain gaming!
              </p>
              <div className="bg-muted rounded-lg p-4 border border-border">
                <p className="text-sm text-muted-foreground mb-2">
                  $CONN4 Token Address:
                </p>
                {/* <div className="bg-background rounded-lg p-3 font-mono text-sm text-foreground break-all border border-border">
                  7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU
                </div> */}
                <p className="text-xs text-muted-foreground mt-2">
                  Available Soon on Solana DEXs • Copy address to buy on your
                  favorite exchange
                </p>
              </div>
            </div>
            {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold text-foreground mb-1">
                  🚀 Early Access
                </h4>
                <p className="text-xs text-muted-foreground">
                  Get priority access to new features
                </p>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold text-foreground mb-1">
                  💰 Rewards
                </h4>
                <p className="text-xs text-muted-foreground">
                  Earn rewards for holding $CONN4
                </p>
              </div>
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-semibold text-foreground mb-1">
                  🎮 Governance
                </h4>
                <p className="text-xs text-muted-foreground">
                  Vote on game improvements
                </p>
              </div>
            </div> */}
          </motion.div>
        </div>
      </div>

      {/* Wallet Connection Popup */}
      {showWalletPopup && (
        <motion.div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}>
          <motion.div
            className="bg-card rounded-lg p-8 border border-border max-w-md w-full mx-4"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">
                Starting Game...
              </h3>
              <p className="text-muted-foreground mb-6">
                Please approve the transaction in your Phantom wallet to start
                the game.
              </p>

              <div className="text-xs text-muted-foreground">
                This will secure your stake and find you an opponent
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Phantom Install Prompt */}
      <PhantomInstallPrompt
        isVisible={showPhantomInstall}
        onClose={() => setShowPhantomInstall(false)}
      />
    </div>
  );
}
