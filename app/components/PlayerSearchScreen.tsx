"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useWallet } from "../hooks/useWallet";
import { useConvexMatchmaking } from "../services/convexMatchmaking";

interface PlayerSearchScreenProps {
  onPlayerFound: (playerWallet: string, gameId?: any) => void;
  onCancel: () => void;
  wagerAmount: number;
}

export default function PlayerSearchScreen({
  onPlayerFound,
  onCancel,
  wagerAmount,
}: PlayerSearchScreenProps) {
  const { walletState } = useWallet();
  const convexMatchmaking = useConvexMatchmaking();

  const [searchStatus, setSearchStatus] = useState<
    "searching" | "found" | "timeout"
  >("searching");
  const [searchTime, setSearchTime] = useState(0);
  const [foundPlayer, setFoundPlayer] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(3);
  const [minSearchTimeReached, setMinSearchTimeReached] = useState(false);

  // Use Convex reactive queries
  const waitingCounts = convexMatchmaking.useWaitingCounts() || {};
  const matchResult = convexMatchmaking.useMatchCheck(
    walletState.address || "",
  );

  // Debug logging
  console.log("PlayerSearchScreen state:", {
    walletAddress: walletState.address,
    wagerAmount,
    waitingCounts,
    matchResult,
    searchStatus,
    convexConnected: !!convexMatchmaking,
  });

  // Join matchmaking queue when component mounts
  useEffect(() => {
    if (!walletState.address) return;

    const joinQueue = async () => {
      try {
        console.log(`Joining queue for ${wagerAmount} SOL...`);
        console.log("Convex matchmaking service:", convexMatchmaking);

        const result = await convexMatchmaking.joinQueue(
          walletState.address!,
          wagerAmount,
        );

        console.log("Join queue result:", result);

        if (result.matched && result.opponent && result.gameId) {
          // Validate that opponent is not the same wallet
          if (result.opponent === walletState.address) {
            console.error("ERROR: Matched with same wallet address!");
            setSearchStatus("searching");
            return;
          }

          console.log(`Immediate match found with: ${result.opponent}`);
          setSearchStatus("found");
          setFoundPlayer(result.opponent);
          setCountdown(3);

          // Start the game after a shorter countdown
          setTimeout(() => {
            onPlayerFound(result.opponent, result.gameId);
          }, 3000);
        } else {
          console.log("No immediate match, waiting for reactive updates...");
        }
      } catch (error) {
        console.error("Failed to join queue:", error);
        console.error("Error details:", error);
      }
    };

    joinQueue();

    // Cleanup: leave queue when component unmounts
    return () => {
      if (walletState.address) {
        convexMatchmaking.leaveQueue(walletState.address);
      }
    };
  }, [walletState.address, wagerAmount]);

  // Watch for matches using Convex reactive query
  useEffect(() => {
    if (
      matchResult?.matched &&
      matchResult.opponent &&
      matchResult.gameId &&
      searchStatus === "searching" &&
      minSearchTimeReached // Only allow matches after minimum search time
    ) {
      // Validate that opponent is not the same wallet
      if (matchResult.opponent === walletState.address) {
        console.error("ERROR: Reactive match with same wallet address!");
        return;
      }

      console.log(
        `Match found with: ${matchResult.opponent} (after ${searchTime}s)`,
      );
      setSearchStatus("found");
      setFoundPlayer(matchResult.opponent);
      setCountdown(3);

      // Start the game after countdown
      setTimeout(() => {
        onPlayerFound(matchResult.opponent, matchResult.gameId);
      }, 3000);
    }
  }, [
    matchResult,
    searchStatus,
    walletState.address,
    minSearchTimeReached,
    searchTime,
  ]);

  // Search timer and minimum search time
  useEffect(() => {
    const timer = setInterval(() => {
      setSearchTime((prev) => {
        const newTime = prev + 1;
        // Set minimum search time reached after 3 seconds
        if (newTime >= 3 && !minSearchTimeReached) {
          setMinSearchTimeReached(true);
        }
        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Countdown timer when player is found
  useEffect(() => {
    if (searchStatus === "found" && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (searchStatus === "found" && countdown === 0) {
      // Auto-start game when countdown reaches 0
      if (foundPlayer) {
        onPlayerFound(foundPlayer);
      }
    }
  }, [searchStatus, countdown, foundPlayer, onPlayerFound]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <motion.div
        className="max-w-2xl w-full bg-card rounded-3xl p-8 border border-border shadow-lg"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}>
        {/* Header */}
        <div className="text-center mb-8">
          <motion.h1
            className="text-3xl font-bold text-foreground mb-2"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}>
            Looking for Players
          </motion.h1>
          <motion.p
            className="text-muted-foreground"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}>
            Waiting for another player to stake {wagerAmount} SOL and join the
            game...
          </motion.p>
        </div>

        {/* Search Status */}
        <AnimatePresence mode="wait">
          {searchStatus === "searching" && (
            <motion.div
              key="searching"
              className="text-center mb-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}>
              {/* Animated Search Icon */}
              <motion.div
                className="w-24 h-24 mx-auto mb-6 relative"
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}>
                <div className="w-full h-full border-4 border-gray-600 border-t-yellow-400 rounded-full"></div>
                <motion.div
                  className="absolute inset-4 border-4 border-gray-700 border-b-red-500 rounded-full"
                  animate={{ rotate: -360 }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "linear",
                  }}></motion.div>
              </motion.div>

              <div className="text-foreground text-xl mb-4">
                Looking for players to stake {wagerAmount} SOL...
              </div>
              <div className="text-muted-foreground text-lg">
                Search time: {formatTime(searchTime)}
              </div>
              <div className="text-muted-foreground text-sm mt-2">
                💰 Total pot will be {(wagerAmount * 2).toFixed(3)} SOL
              </div>
              <div className="text-blue-400 text-sm mt-2">
                👥 {waitingCounts[wagerAmount] || 0} players waiting for this
                wager
              </div>

              {/* Search Animation Dots */}
              <div className="flex justify-center space-x-2 mt-4">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-3 h-3 bg-gradient-primary rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}

          {searchStatus === "found" && (
            <motion.div
              key="found"
              className="text-center mb-8"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5 }}>
              {/* Success Icon */}
              <motion.div
                className="w-24 h-24 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200, damping: 10 }}>
                <svg
                  className="w-12 h-12 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </motion.div>

              <div className="text-foreground text-xl mb-6">
                🎉 Opponent Found & Staked!
              </div>
              <div className="text-green-400 text-sm mb-4">
                Both players have staked {wagerAmount} SOL
              </div>

              {/* Player Color Assignments */}
              <div className="flex justify-center gap-8 mb-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-red-500 rounded-full mx-auto mb-2 flex items-center justify-center">
                    <img
                      src="/red-coin.png"
                      alt="Your color"
                      className="w-12 h-12"
                    />
                  </div>
                  <div className="text-foreground font-semibold">You</div>
                  <div className="text-red-400 text-sm">Red Player</div>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-yellow-400 rounded-full mx-auto mb-2 flex items-center justify-center">
                    <img
                      src="/yellow-coin.png"
                      alt="Opponent color"
                      className="w-12 h-12"
                    />
                  </div>
                  <div className="text-foreground font-semibold">Opponent</div>
                  <div className="text-yellow-400 text-sm">Yellow Player</div>
                </div>
              </div>

              <div className="bg-muted rounded-xl p-4 mb-6 border border-border">
                <div className="text-muted-foreground text-sm mb-2">
                  Opponent Wallet:
                </div>
                <div className="text-foreground font-mono text-sm break-all">
                  {foundPlayer}
                </div>
              </div>

              {/* Countdown */}
              <div className="text-center">
                <div className="text-foreground text-lg mb-2">
                  Game Starting in
                </div>
                <motion.div
                  className="text-4xl font-bold text-green-400 mb-4"
                  key={countdown}
                  initial={{ scale: 1.2 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 0.3 }}>
                  {countdown}s
                </motion.div>
                <div className="w-full bg-muted rounded-full h-2">
                  <motion.div
                    className="h-2 rounded-full bg-gradient-primary"
                    initial={{ width: "100%" }}
                    animate={{ width: `${(countdown / 5) * 100}%` }}
                    transition={{ duration: 1, ease: "linear" }}
                  />
                </div>
              </div>
            </motion.div>
          )}

          {searchStatus === "timeout" && (
            <motion.div
              key="timeout"
              className="text-center mb-8"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5 }}>
              {/* Timeout Icon */}
              <motion.div
                className="w-24 h-24 mx-auto mb-6 bg-red-500 rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ type: "spring", stiffness: 200, damping: 10 }}>
                <svg
                  className="w-12 h-12 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </motion.div>

              <div className="text-white text-xl mb-4">Search Timeout</div>
              <div className="text-gray-400 mb-6">
                No players found. Try again?
              </div>

              <div className="flex gap-4 justify-center">
                <motion.button
                  onClick={() => {
                    setSearchStatus("searching");
                    setSearchTime(0);
                  }}
                  className="btn-gradient px-8 py-3 rounded-xl font-semibold"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}>
                  Search Again
                </motion.button>
                <motion.button
                  onClick={onCancel}
                  className="bg-muted hover:bg-muted/80 text-foreground px-8 py-3 rounded-xl font-semibold transition-colors border border-border"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}>
                  Cancel
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Cancel Button (always visible during search) */}
        {searchStatus === "searching" && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}>
            <motion.button
              onClick={onCancel}
              className="bg-muted hover:bg-muted/80 text-foreground px-8 py-3 rounded-xl font-semibold transition-colors border border-border"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}>
              Cancel Search
            </motion.button>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
