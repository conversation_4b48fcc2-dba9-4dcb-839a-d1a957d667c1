"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import { useWallet } from "../hooks/useWallet";
import { Id } from "../../convex/_generated/dataModel";

interface PlayerSearchScreenProps {
  onPlayerFound: (playerWallet: string, gameId?: Id<"gameSessions">) => void;
  onCancel: () => void;
  wagerAmount: number;
}

export default function PlayerSearchScreen({
  onPlayerFound,
  onCancel,
  wagerAmount,
}: PlayerSearchScreenProps) {
  const [searchTime, setSearchTime] = useState(0);
  const [isSearching, setIsSearching] = useState(false);
  const { walletState } = useWallet();
  const convexMatchmaking = useConvexMatchmaking();

  // Check for matches
  const matchResult = convexMatchmaking.useMatchCheck(
    walletState.address || "",
  );

  // Start searching when component mounts
  useEffect(() => {
    if (walletState.address && !isSearching) {
      setIsSearching(true);
      convexMatchmaking.joinQueue(walletState.address, wagerAmount);
    }
  }, [walletState.address, wagerAmount, convexMatchmaking, isSearching]);

  // Handle match found
  useEffect(() => {
    if (matchResult?.matched && matchResult.gameId) {
      console.log("Match found!", matchResult);
      onPlayerFound(matchResult.opponentWallet, matchResult.gameId);
    }
  }, [matchResult, onPlayerFound]);

  // Timer for search duration
  useEffect(() => {
    const timer = setInterval(() => {
      setSearchTime((prev) => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleCancel = async () => {
    if (walletState.address) {
      await convexMatchmaking.leaveQueue(walletState.address, wagerAmount);
    }
    onCancel();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <motion.div
        className="bg-card rounded-lg p-8 border border-border max-w-md w-full mx-4"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center">
          {/* Searching Animation */}
          <motion.div
            className="w-20 h-20 bg-gradient-primary rounded-full mx-auto mb-6 flex items-center justify-center"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </motion.div>

          <h2 className="text-2xl font-bold text-foreground mb-4">
            Finding Opponent...
          </h2>

          <div className="space-y-4 mb-6">
            <div className="text-muted-foreground">
              <div className="text-lg font-semibold">
                Wager: {wagerAmount} SOL
              </div>
              <div className="text-sm">
                Total Pot: {(wagerAmount * 2).toFixed(3)} SOL
              </div>
            </div>

            <div className="text-muted-foreground">
              <div className="text-sm">Search Time</div>
              <div className="text-xl font-mono">{formatTime(searchTime)}</div>
            </div>

            <div className="text-xs text-muted-foreground">
              Looking for players with the same wager amount...
            </div>
          </div>

          <motion.button
            onClick={handleCancel}
            className="w-full py-3 px-6 bg-muted text-foreground rounded-lg hover:bg-muted/80 transition-colors"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Cancel Search
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
}
