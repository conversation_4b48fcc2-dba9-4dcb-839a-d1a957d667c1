"use client";

import { useWallet as useSolanaWallet } from "@solana/wallet-adapter-react";
import { motion } from "framer-motion";

export default function WalletAdapterDebug() {
  const {
    wallet,
    wallets,
    connected,
    connecting,
    disconnecting,
    publicKey,
    connect,
    disconnect,
    select,
  } = useSolanaWallet();

  return (
    <motion.div
      className="fixed bottom-20 left-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50 max-w-sm"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}>
      <div className="text-sm">
        <div className="text-foreground font-semibold mb-3">
          ⚙️ Wallet Adapter Debug
        </div>

        <div className="space-y-2 text-xs mb-4">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Available Wallets:</span>
            <span className="text-foreground">{wallets.length}</span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Current Wallet:</span>
            <span className="text-foreground">
              {wallet?.adapter?.name || "None"}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connected:</span>
            <span className={connected ? "text-green-400" : "text-red-400"}>
              {connected ? "✅ Yes" : "❌ No"}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connecting:</span>
            <span
              className={
                connecting ? "text-yellow-400" : "text-muted-foreground"
              }>
              {connecting ? "🔄 Yes" : "No"}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Disconnecting:</span>
            <span
              className={
                disconnecting ? "text-yellow-400" : "text-muted-foreground"
              }>
              {disconnecting ? "🔄 Yes" : "No"}
            </span>
          </div>

          {publicKey && (
            <div className="mt-2 p-2 bg-muted rounded text-xs">
              <div className="text-muted-foreground">Public Key:</div>
              <div className="font-mono text-foreground break-all">
                {publicKey.toString()}
              </div>
            </div>
          )}

          <div className="mt-2 p-2 bg-muted rounded text-xs">
            <div className="text-muted-foreground">Wallet Ready:</div>
            <div className="text-foreground">
              {wallet?.adapter?.readyState || "Unknown"}
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {!wallet && wallets.length > 0 && select && (
            <button
              onClick={() => {
                const phantomWallet = wallets.find(
                  (w) => w.adapter.name === "Phantom",
                );
                if (phantomWallet) {
                  console.log("Manually selecting Phantom wallet");
                  select(phantomWallet.adapter.name);
                }
              }}
              className="w-full py-1 px-2 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors">
              Select Phantom
            </button>
          )}

          {wallet && !connected && !connecting && (
            <button
              onClick={() => {
                console.log("Direct adapter connect clicked");
                connect().catch(console.error);
              }}
              className="w-full py-1 px-2 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors">
              Direct Connect
            </button>
          )}

          {connected && (
            <button
              onClick={() => {
                console.log("Direct adapter disconnect clicked");
                disconnect().catch(console.error);
              }}
              className="w-full py-1 px-2 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition-colors">
              Direct Disconnect
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
}
