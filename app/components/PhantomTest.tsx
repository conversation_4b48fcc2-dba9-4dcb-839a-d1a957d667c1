"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";

export default function PhantomTest() {
  const [phantomStatus, setPhantomStatus] = useState<{
    installed: boolean;
    detected: boolean;
    connected: boolean;
    address: string | null;
    error: string | null;
  }>({
    installed: false,
    detected: false,
    connected: false,
    address: null,
    error: null
  });

  useEffect(() => {
    checkPhantomStatus();
  }, []);

  const checkPhantomStatus = () => {
    if (typeof window === 'undefined') return;

    const phantom = (window as any).phantom?.solana;
    const installed = !!phantom;
    const detected = !!phantom?.isPhantom;
    const connected = !!phantom?.isConnected;

    setPhantomStatus({
      installed,
      detected,
      connected,
      address: connected ? phantom.publicKey?.toString() : null,
      error: null
    });

    console.log('Phantom Status Check:', {
      installed,
      detected,
      connected,
      phantom: (window as any).phantom,
      solana: phantom
    });
  };

  const testDirectConnection = async () => {
    try {
      if (typeof window === 'undefined') {
        throw new Error('Window not available');
      }

      const phantom = (window as any).phantom?.solana;
      if (!phantom) {
        throw new Error('Phantom not found');
      }

      console.log('Attempting direct Phantom connection...');
      const response = await phantom.connect();
      console.log('Direct connection response:', response);

      setPhantomStatus(prev => ({
        ...prev,
        connected: true,
        address: response.publicKey.toString(),
        error: null
      }));
    } catch (error: any) {
      console.error('Direct connection failed:', error);
      setPhantomStatus(prev => ({
        ...prev,
        error: error.message
      }));
    }
  };

  return (
    <motion.div
      className="fixed top-20 left-4 bg-card border border-border rounded-lg p-4 shadow-lg z-50 max-w-sm"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}>
      <div className="text-sm">
        <div className="text-foreground font-semibold mb-3">🧪 Phantom Test</div>
        
        <div className="space-y-2 text-xs mb-4">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Installed:</span>
            <span className={phantomStatus.installed ? "text-green-400" : "text-red-400"}>
              {phantomStatus.installed ? "✅ Yes" : "❌ No"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Detected:</span>
            <span className={phantomStatus.detected ? "text-green-400" : "text-red-400"}>
              {phantomStatus.detected ? "✅ Yes" : "❌ No"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connected:</span>
            <span className={phantomStatus.connected ? "text-green-400" : "text-red-400"}>
              {phantomStatus.connected ? "✅ Yes" : "❌ No"}
            </span>
          </div>
          
          {phantomStatus.address && (
            <div className="mt-2 p-2 bg-muted rounded text-xs">
              <div className="text-muted-foreground">Address:</div>
              <div className="font-mono text-foreground break-all">
                {phantomStatus.address}
              </div>
            </div>
          )}
          
          {phantomStatus.error && (
            <div className="mt-2 p-2 bg-red-500/10 rounded border border-red-500/20">
              <div className="text-red-400 font-semibold">Error:</div>
              <div className="text-red-300 text-xs">{phantomStatus.error}</div>
            </div>
          )}
        </div>
        
        <div className="space-y-2">
          <button
            onClick={checkPhantomStatus}
            className="w-full py-1 px-2 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors">
            Refresh Status
          </button>
          
          {phantomStatus.installed && !phantomStatus.connected && (
            <button
              onClick={testDirectConnection}
              className="w-full py-1 px-2 bg-green-500 text-white rounded text-xs hover:bg-green-600 transition-colors">
              Test Direct Connection
            </button>
          )}
        </div>
      </div>
    </motion.div>
  );
}
