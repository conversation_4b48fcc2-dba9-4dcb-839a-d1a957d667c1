// Simple in-memory matchmaking service
// In production, this would be a real backend service

export interface WaitingPlayer {
  wallet: string;
  wagerAmount: number;
  timestamp: number;
  id: string;
}

class MatchmakingService {
  private waitingPlayers: Map<number, WaitingPlayer[]> = new Map();
  private listeners: Set<(counts: Record<number, number>) => void> = new Set();

  // Add player to waiting queue
  addPlayer(wallet: string, wagerAmount: number): string {
    const playerId = `${wallet}-${Date.now()}`;

    // Check if player is already in queue for this wager
    const existingQueue = this.waitingPlayers.get(wagerAmount) || [];
    const alreadyInQueue = existingQueue.some((p) => p.wallet === wallet);

    if (alreadyInQueue) {
      console.log(`Player ${wallet} already in queue for ${wagerAmount} SOL`);
      return playerId;
    }

    const player: WaitingPlayer = {
      wallet,
      wagerAmount,
      timestamp: Date.now(),
      id: playerId,
    };

    // Get or create queue for this wager amount
    const queue = this.waitingPlayers.get(wagerAmount) || [];
    queue.push(player);
    this.waitingPlayers.set(wagerAmount, queue);

    console.log(
      `Player ${wallet.slice(0, 8)}... joined queue for ${wagerAmount} SOL (${
        queue.length
      } total)`,
    );
    this.notifyListeners();

    return playerId;
  }

  // Remove player from waiting queue
  removePlayer(playerId: string): void {
    for (const [wagerAmount, queue] of this.waitingPlayers.entries()) {
      const index = queue.findIndex((p) => p.id === playerId);
      if (index !== -1) {
        queue.splice(index, 1);
        if (queue.length === 0) {
          this.waitingPlayers.delete(wagerAmount);
        }
        console.log(`Player ${playerId} removed from queue`);
        this.notifyListeners();
        break;
      }
    }
  }

  // Check for matches in a specific wager queue
  private checkForMatch(wagerAmount: number): WaitingPlayer | null {
    const queue = this.waitingPlayers.get(wagerAmount);
    if (!queue || queue.length < 2) return null;

    // Match the first two players
    const player1 = queue.shift()!;
    const player2 = queue.shift()!;

    console.log(
      `Match found! ${player1.wallet} vs ${player2.wallet} for ${wagerAmount} SOL`,
    );

    // Clean up empty queue
    if (queue.length === 0) {
      this.waitingPlayers.delete(wagerAmount);
    }

    this.notifyListeners();

    // Return the opponent for the current player
    return player2;
  }

  // Get waiting player counts for each wager amount
  getWaitingCounts(): Record<number, number> {
    const counts: Record<number, number> = {};
    for (const [wagerAmount, queue] of this.waitingPlayers.entries()) {
      counts[wagerAmount] = queue.length;
    }
    return counts;
  }

  // Subscribe to waiting count updates
  onWaitingCountsChange(
    callback: (counts: Record<number, number>) => void,
  ): () => void {
    this.listeners.add(callback);
    // Return unsubscribe function
    return () => this.listeners.delete(callback);
  }

  // Notify all listeners of count changes
  private notifyListeners(): void {
    const counts = this.getWaitingCounts();
    this.listeners.forEach((callback) => callback(counts));
  }

  // Try to find a real match first, then simulate if needed
  simulateMatch(wagerAmount: number): WaitingPlayer | null {
    // First try to find a real match
    const realMatch = this.checkForMatch(wagerAmount);
    if (realMatch) {
      return realMatch;
    }

    // If no real players, create a fake opponent for testing
    const fakeOpponent: WaitingPlayer = {
      wallet: `${Math.random().toString(36).substring(2, 8)}...${Math.random()
        .toString(36)
        .substring(2, 6)}`,
      wagerAmount,
      timestamp: Date.now(),
      id: `fake-${Date.now()}`,
    };

    console.log(
      `No real players found. Simulated opponent: ${fakeOpponent.wallet}`,
    );
    return fakeOpponent;
  }

  // Get all waiting players for debugging
  getWaitingPlayers(): Map<number, WaitingPlayer[]> {
    return new Map(this.waitingPlayers);
  }

  // Force check for matches (for testing)
  forceCheckMatches(): void {
    for (const wagerAmount of this.waitingPlayers.keys()) {
      this.checkForMatch(wagerAmount);
    }
  }
}

// Export singleton instance
export const matchmakingService = new MatchmakingService();
