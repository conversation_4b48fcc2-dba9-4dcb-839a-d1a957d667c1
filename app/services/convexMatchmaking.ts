import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

export interface ConvexMatchmakingService {
  // Join matchmaking queue
  joinQueue: (
    walletAddress: string,
    wagerAmount: number,
  ) => Promise<{
    playerId: Id<"waitingPlayers">;
    matched: boolean;
    gameId?: Id<"gameSessions">;
    opponent?: string;
  }>;

  // Leave matchmaking queue
  leaveQueue: (walletAddress: string) => Promise<void>;

  // Get waiting counts (reactive)
  useWaitingCounts: () => Record<number, number> | undefined;

  // Check for match (reactive)
  useMatchCheck: (walletAddress: string) =>
    | {
        matched: boolean;
        gameId?: Id<"gameSessions">;
        opponent?: string;
        playerNumber?: number;
      }
    | undefined;

  // Get game state (reactive)
  useGameState: (gameId: Id<"gameSessions"> | undefined) => any;

  // Make a move
  makeMove: (
    gameId: Id<"gameSessions">,
    walletAddress: string,
    column: number,
  ) => Promise<any>;

  // Auto-drop on timeout
  autoDropOnTimeout: (gameId: Id<"gameSessions">) => Promise<any>;

  // Start game
  startGame: (gameId: Id<"gameSessions">) => Promise<void>;

  // Clear all data (for testing)
  clearAll: () => Promise<void>;
}

export function useConvexMatchmaking(): ConvexMatchmakingService {
  const joinQueueMutation = useMutation(api.matchmaking.joinQueue);
  const leaveQueueMutation = useMutation(api.matchmaking.leaveQueue);
  const makeMoveMutation = useMutation(api.games.makeMove);
  const autoDropMutation = useMutation(api.games.autoDropOnTimeout);
  const startGameMutation = useMutation(api.games.startGame);
  const clearAllMutation = useMutation(api.matchmaking.clearAll);

  return {
    joinQueue: async (walletAddress: string, wagerAmount: number) => {
      return await joinQueueMutation({ walletAddress, wagerAmount });
    },

    leaveQueue: async (walletAddress: string) => {
      await leaveQueueMutation({ walletAddress });
    },

    useWaitingCounts: () => {
      return useQuery(api.matchmaking.getWaitingCounts);
    },

    useMatchCheck: (walletAddress: string) => {
      return useQuery(
        api.matchmaking.checkForMatch,
        walletAddress ? { walletAddress } : "skip",
      );
    },

    useGameState: (gameId: Id<"gameSessions"> | undefined) => {
      return useQuery(api.games.getGame, gameId ? { gameId } : "skip");
    },

    makeMove: async (
      gameId: Id<"gameSessions">,
      walletAddress: string,
      column: number,
    ) => {
      return await makeMoveMutation({ gameId, walletAddress, column });
    },

    autoDropOnTimeout: async (gameId: Id<"gameSessions">) => {
      return await autoDropMutation({ gameId });
    },

    startGame: async (gameId: Id<"gameSessions">) => {
      await startGameMutation({ gameId });
    },

    clearAll: async () => {
      await clearAllMutation();
    },
  };
}
