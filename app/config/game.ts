import { PublicKey } from "@solana/web3.js";

// Game Configuration
export const GAME_CONFIG = {
  // Platform fee percentage (5% = 0.05)
  PLATFORM_FEE_PERCENTAGE: 0.05,

  // Developer wallet address (replace with your actual wallet address)
  DEVELOPER_WALLET: new PublicKey(
    "AWKkqeEFHsC8LqPcYAf1ivWkAwji2zZmiPWvpXacCNtn",
  ), // System Program ID as placeholder

  // Network settings
  NETWORK: "devnet", // or "mainnet-beta" for production

  // Transaction settings
  CONFIRMATION_LEVEL: "confirmed" as const,

  // Game settings
  MOVE_TIMEOUT_SECONDS: 30,
  SEARCH_TIMEOUT_SECONDS: 300, // 5 minutes
} as const;

// Helper functions
export const calculatePlatformFee = (wagerAmount: number): number => {
  return wagerAmount * GAME_CONFIG.PLATFORM_FEE_PERCENTAGE;
};

export const calculateTotalStake = (wagerAmount: number): number => {
  return wagerAmount + calculatePlatformFee(wagerAmount);
};

export const calculateWinnerPayout = (totalWagerAmount: number): number => {
  // Winner gets both players' wager amounts (no platform fee deducted from winnings)
  return totalWagerAmount * 2;
};

export const formatSOL = (amount: number, decimals: number = 4): string => {
  return amount.toFixed(decimals);
};

// Validate developer wallet
export const isDeveloperWalletValid = (): boolean => {
  try {
    // Check if it's not the placeholder address
    return !GAME_CONFIG.DEVELOPER_WALLET.equals(
      new PublicKey("********************************"),
    );
  } catch {
    return false;
  }
};
