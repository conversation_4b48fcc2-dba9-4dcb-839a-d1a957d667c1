"use client";

import { useState } from "react";
import { useConvexMatchmaking } from "../services/convexMatchmaking";
import { useCustomWallet } from "../hooks/useCustomWallet";

export default function TestPage() {
  const { walletState } = useCustomWallet();
  const convexMatchmaking = useConvexMatchmaking();

  const waitingCounts = convexMatchmaking.useWaitingCounts();
  const matchResult = convexMatchmaking.useMatchCheck(
    walletState.address || "",
  );

  const [testResult, setTestResult] = useState<string>("");

  const testJoinQueue = async () => {
    if (!walletState.address) {
      setTestResult("No wallet connected");
      return;
    }

    try {
      setTestResult("Testing join queue...");
      const result = await convexMatchmaking.joinQueue(
        walletState.address,
        0.001,
      );
      setTestResult(`Join result: ${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      setTestResult(`Error: ${error}`);
    }
  };

  const testLeaveQueue = async () => {
    if (!walletState.address) {
      setTestResult("No wallet connected");
      return;
    }

    try {
      setTestResult("Testing leave queue...");
      await convexMatchmaking.leaveQueue(walletState.address);
      setTestResult("Left queue successfully");
    } catch (error) {
      setTestResult(`Error: ${error}`);
    }
  };

  const testClearAll = async () => {
    try {
      setTestResult("Clearing all data...");
      await convexMatchmaking.clearAll();
      setTestResult("All data cleared successfully");
    } catch (error) {
      setTestResult(`Error: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Convex Matchmaking Test</h1>

        <div className="space-y-6">
          {/* Wallet Status */}
          <div className="bg-card p-4 rounded-lg border">
            <h2 className="text-lg font-semibold mb-2">Wallet Status</h2>
            <p>Connected: {walletState.connected ? "Yes" : "No"}</p>
            <p>Address: {walletState.address || "None"}</p>
            {!walletState.connected && (
              <div className="mt-2 text-sm text-muted-foreground">
                Use the "Connect Wallet" button in the top-right corner
              </div>
            )}
          </div>

          {/* Waiting Counts */}
          <div className="bg-card p-4 rounded-lg border">
            <h2 className="text-lg font-semibold mb-2">Waiting Counts</h2>
            <pre className="text-sm bg-muted p-2 rounded">
              {JSON.stringify(waitingCounts, null, 2)}
            </pre>
          </div>

          {/* Match Result */}
          <div className="bg-card p-4 rounded-lg border">
            <h2 className="text-lg font-semibold mb-2">Match Result</h2>
            <pre className="text-sm bg-muted p-2 rounded">
              {JSON.stringify(matchResult, null, 2)}
            </pre>
          </div>

          {/* Test Buttons */}
          <div className="bg-card p-4 rounded-lg border">
            <h2 className="text-lg font-semibold mb-2">Test Functions</h2>
            <div className="flex gap-2 mb-4">
              <button
                onClick={testJoinQueue}
                className="bg-green-500 text-white px-4 py-2 rounded">
                Test Join Queue
              </button>
              <button
                onClick={testLeaveQueue}
                className="bg-red-500 text-white px-4 py-2 rounded">
                Test Leave Queue
              </button>
              <button
                onClick={testClearAll}
                className="bg-orange-500 text-white px-4 py-2 rounded">
                Clear All Data
              </button>
            </div>

            {testResult && (
              <div className="bg-muted p-2 rounded">
                <h3 className="font-semibold mb-1">Test Result:</h3>
                <pre className="text-sm whitespace-pre-wrap">{testResult}</pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
