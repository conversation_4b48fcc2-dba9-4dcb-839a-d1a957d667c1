import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "./components/ThemeProvider";
import { ConvexProvider } from "./providers/ConvexProvider";
import WalletContextProvider from "./components/WalletProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Connect4.fun - Blockchain Gaming on Solana",
  description:
    "Play Connect 4 with real SOL stakes on the Solana blockchain. Fair, transparent, and instant payouts.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <ConvexProvider>
          <ThemeProvider defaultTheme="dark" storageKey="connect4-theme">
            <WalletContextProvider>{children}</WalletContextProvider>
          </ThemeProvider>
        </ConvexProvider>
      </body>
    </html>
  );
}
