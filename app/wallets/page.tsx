"use client";

import { useState } from "react";
import {
  generateTestWallets,
  createTestWallet,
  WalletInfo,
} from "../utils/walletGenerator";
import { useCustomWallet } from "../hooks/useCustomWallet";

export default function WalletsPage() {
  const [testWallets, setTestWallets] = useState<WalletInfo[]>([]);
  const { walletState, connectExistingWallet, requestAirdrop, disconnect } =
    useCustomWallet();

  const handleGenerateWallets = async () => {
    const wallets = await generateTestWallets();
    setTestWallets(wallets);
  };

  const handleConnectWallet = async (privateKey: string) => {
    try {
      await connectExistingWallet(privateKey);
      alert("Wallet connected successfully!");
    } catch (error) {
      alert("Failed to connect wallet");
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert("Copied to clipboard!");
  };

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🔑 Wallet Testing Center</h1>

        {/* Current Wallet Status */}
        <div className="bg-card p-6 rounded-lg border mb-8">
          <h2 className="text-xl font-semibold mb-4">Current Wallet</h2>
          {walletState.connected ? (
            <div className="space-y-2">
              <p>
                <strong>Address:</strong> {walletState.address}
              </p>
              <p>
                <strong>Balance:</strong> {walletState.balance.toFixed(4)} SOL
              </p>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={requestAirdrop}
                  className="bg-green-500 text-white px-4 py-2 rounded">
                  💰 Add 2 SOL
                </button>
                <button
                  onClick={disconnect}
                  className="bg-red-500 text-white px-4 py-2 rounded">
                  Disconnect
                </button>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">No wallet connected</p>
          )}
        </div>

        {/* Generate Test Wallets */}
        <div className="bg-card p-6 rounded-lg border mb-8">
          <h2 className="text-xl font-semibold mb-4">Generate Test Wallets</h2>
          <p className="text-muted-foreground mb-4">
            Create two different wallets for testing multiplayer matchmaking
          </p>
          <button
            onClick={handleGenerateWallets}
            className="bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold">
            Generate 2 Test Wallets
          </button>
        </div>

        {/* Test Wallets List */}
        {testWallets.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Generated Test Wallets</h2>

            {testWallets.map((wallet, index) => (
              <div key={index} className="bg-card p-6 rounded-lg border">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold text-green-500">
                    {wallet.name}
                  </h3>
                  <button
                    onClick={() => handleConnectWallet(wallet.privateKey)}
                    className="bg-green-500 text-white px-4 py-2 rounded">
                    Connect This Wallet
                  </button>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-semibold text-muted-foreground">
                      Address:
                    </label>
                    <div className="flex items-center space-x-2 mt-1">
                      <code className="bg-muted p-2 rounded text-sm flex-1 font-mono">
                        {wallet.address}
                      </code>
                      <button
                        onClick={() => copyToClipboard(wallet.address)}
                        className="bg-blue-500 text-white px-3 py-1 rounded text-sm">
                        Copy
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-semibold text-muted-foreground">
                      🌱 Seed Phrase (12 words):
                    </label>
                    <div className="flex items-center space-x-2 mt-1">
                      <code className="bg-muted p-2 rounded text-sm flex-1 font-mono break-all">
                        {wallet.seedPhrase}
                      </code>
                      <button
                        onClick={() => copyToClipboard(wallet.seedPhrase)}
                        className="bg-green-500 text-white px-3 py-1 rounded text-sm">
                        Copy
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-semibold text-muted-foreground">
                      🔑 Private Key (Advanced):
                    </label>
                    <div className="flex items-center space-x-2 mt-1">
                      <code className="bg-muted p-2 rounded text-sm flex-1 font-mono break-all">
                        {wallet.privateKey.slice(0, 50)}...
                      </code>
                      <button
                        onClick={() => copyToClipboard(wallet.privateKey)}
                        className="bg-orange-500 text-white px-3 py-1 rounded text-sm">
                        Copy
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="font-semibold text-yellow-800 mb-2">
                🧪 How to Test Multiplayer (with Seed Phrases):
              </h3>
              <div className="text-yellow-600 text-xs mb-2">
                💡 Use the 🌱 Seed Phrase to import wallets - easier than
                private keys!
              </div>
              <ol className="text-yellow-700 text-sm space-y-1">
                <li>
                  1. <strong>Browser 1:</strong> Connect "Player 1 (Alice)"
                  wallet
                </li>
                <li>
                  2. <strong>Browser 2 (Incognito):</strong> Connect "Player 2
                  (Bob)" wallet
                </li>
                <li>
                  3. <strong>Both:</strong> Go to main game and select same
                  wager amount
                </li>
                <li>
                  4. <strong>Both:</strong> Click "Start Game" - should match
                  instantly!
                </li>
                <li>
                  5. <strong>Play:</strong> Take turns making moves in real-time
                </li>
              </ol>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">
                💡 Demo Mode Features:
              </h3>
              <ul className="text-green-700 text-sm space-y-1">
                <li>
                  • <strong>Instant Faucet:</strong> Add SOL instantly without
                  rate limits
                </li>
                <li>
                  • <strong>Simulated Transactions:</strong> No real blockchain
                  delays
                </li>
                <li>
                  • <strong>Instant Payouts:</strong> Winners get SOL
                  immediately
                </li>
                <li>
                  • <strong>No Network Fees:</strong> Perfect for testing
                  gameplay
                </li>
              </ul>
            </div>

            <div className="flex space-x-4">
              <a
                href="/"
                className="bg-green-500 text-white px-6 py-3 rounded-lg font-semibold">
                Go to Main Game
              </a>
              <a
                href="/test"
                className="bg-blue-500 text-white px-6 py-3 rounded-lg font-semibold">
                Go to Debug Page
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
