import { Keypair } from "@solana/web3.js";
import * as bip39 from "bip39";
import { derivePath } from "ed25519-hd-key";

export interface WalletInfo {
  address: string;
  seedPhrase: string;
  privateKey: string;
  name: string;
}

// Pre-generated test wallets for easy testing
export const TEST_WALLETS: WalletInfo[] = [
  {
    name: "Player 1 (Alice)",
    address: "",
    seedPhrase: "",
    privateKey: "",
  },
  {
    name: "<PERSON> 2 (<PERSON>)",
    address: "",
    seedPhrase: "",
    privateKey: "",
  },
];

// Generate test wallets with seed phrases
export async function generateTestWallets(): Promise<WalletInfo[]> {
  const wallets: WalletInfo[] = [];

  for (let i = 0; i < 2; i++) {
    // Generate seed phrase
    const seedPhrase = bip39.generateMnemonic();

    // Derive keypair from seed phrase
    const seed = await bip39.mnemonicToSeed(seedPhrase);
    const derivedSeed = derivePath(
      "m/44'/501'/0'/0'",
      seed.toString("hex"),
    ).key;
    const keypair = Keypair.fromSeed(derivedSeed);

    const privateKeyArray = Array.from(keypair.secretKey);
    const privateKeyString = JSON.stringify(privateKeyArray);

    wallets.push({
      name: `Player ${i + 1} (${i === 0 ? "Alice" : "Bob"})`,
      address: keypair.publicKey.toString(),
      seedPhrase: seedPhrase,
      privateKey: privateKeyString,
    });
  }

  return wallets;
}

// Create a specific wallet for testing
export async function createTestWallet(name: string): Promise<WalletInfo> {
  // Generate seed phrase
  const seedPhrase = bip39.generateMnemonic();

  // Derive keypair from seed phrase
  const seed = await bip39.mnemonicToSeed(seedPhrase);
  const derivedSeed = derivePath("m/44'/501'/0'/0'", seed.toString("hex")).key;
  const keypair = Keypair.fromSeed(derivedSeed);

  const privateKeyArray = Array.from(keypair.secretKey);
  const privateKeyString = JSON.stringify(privateKeyArray);

  return {
    name,
    address: keypair.publicKey.toString(),
    seedPhrase: seedPhrase,
    privateKey: privateKeyString,
  };
}
