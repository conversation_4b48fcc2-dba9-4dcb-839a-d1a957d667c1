# Connect4 Two-Player Testing Guide

## Prerequisites

1. **Install Phantom Wallet**
   - Download from [phantom.app](https://phantom.app)
   - Install the browser extension
   - Create at least 2 different wallet accounts for testing

2. **Get Devnet SOL**
   - Switch Phantom to Devnet (Settings > Developer Settings > Change Network > Devnet)
   - Get free devnet SOL from [solfaucet.com](https://solfaucet.com) or [solana faucet](https://faucet.solana.com)
   - Each wallet should have at least 1-2 SOL for testing

## Two-Player Testing Setup

### Method 1: Two Browser Profiles
1. **Chrome/Edge**: Create two different browser profiles
2. **Firefox**: Use regular window + private window
3. Install Phantom in both and create different wallets
4. Open the game in both profiles simultaneously

### Method 2: Two Different Browsers
1. Use Chrome for Player 1, Firefox for Player 2
2. Install Phantom in both browsers
3. Create different wallet accounts
4. Open the game in both browsers

### Method 3: Incognito/Private Mode
1. Regular window: Player 1 wallet
2. Incognito/Private window: Player 2 wallet
3. Note: You'll need to reconnect wallets each time

## Testing Flow

### 1. Player 1 Setup
```
1. Open game in first browser/profile
2. Connect Phantom wallet (Account A)
3. Select wager amount (start with 0.001 SOL)
4. Click "Connect Wallet & Start Game"
5. Wait for opponent matching
```

### 2. Player 2 Setup
```
1. Open game in second browser/profile
2. Connect Phantom wallet (Account B - different from Player 1)
3. Select SAME wager amount as Player 1
4. Click "Connect Wallet & Start Game"
5. Should match with Player 1
```

### 3. Gameplay Testing
```
1. Players take turns clicking columns
2. Each move requires transaction approval in Phantom
3. Watch for:
   - Transaction processing in timer cards
   - Balance updates after moves
   - Game state synchronization
   - Winner determination and payouts
```

## What to Test

### ✅ Wallet Connection
- [ ] Phantom detection and connection
- [ ] Wallet address display
- [ ] Balance fetching and display
- [ ] Connection error handling

### ✅ Game Flow
- [ ] Player matching with same wager
- [ ] Turn-based gameplay
- [ ] Move transaction processing
- [ ] Timer functionality
- [ ] Game board updates

### ✅ Transactions
- [ ] Move staking transactions
- [ ] Transaction confirmation
- [ ] Balance updates
- [ ] Error handling for failed transactions
- [ ] Gas fee deduction

### ✅ Game Completion
- [ ] Winner detection
- [ ] Prize distribution
- [ ] Game over screen
- [ ] Rematch functionality
- [ ] New game options

## Troubleshooting

### Common Issues
1. **Wallet not connecting**: Ensure Phantom is unlocked and on Devnet
2. **No opponent found**: Both players must select same wager amount
3. **Transaction fails**: Check sufficient balance for wager + gas fees
4. **Game not syncing**: Refresh both browser windows

### Debug Information
- Check browser console for errors
- Verify network is set to Devnet in Phantom
- Ensure both wallets have sufficient SOL balance
- Check transaction signatures in Solana Explorer (devnet)

## Development Notes

- Game uses Solana Devnet for testing
- Minimum transaction amount: 0.001 SOL
- Gas fees: ~0.001 SOL per transaction
- Transactions are confirmed before moves are processed
- Real blockchain integration with Phantom wallet adapter
